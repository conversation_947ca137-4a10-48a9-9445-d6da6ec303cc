{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"deploy": "wrangler deploy", "cf-typegen": "wrangler types --env-interface Env", "dev": "vite dev", "build": "vite build && tsc --noEmit", "start": "node .output/server/index.mjs"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/db": "^0.1.4", "@tanstack/electric-db-collection": "^0.1.4", "@tanstack/form-core": "^1.19.2", "@tanstack/query-core": "^5.85.3", "@tanstack/query-db-collection": "^0.2.2", "@tanstack/react-db": "^0.1.4", "@tanstack/react-form": "^1.14.2", "@tanstack/react-query": "^5.84.1", "@tanstack/react-router": "^1.129.8", "@tanstack/react-router-devtools": "^1.129.8", "@tanstack/react-start": "^1.129.8", "@tanstack/react-store": "^0.7.3", "@tanstack/react-table": "^8.21.3", "@tanstack/start": "^1.120.20", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.3", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "resend": "^4.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "valibot": "^1.1.0", "wrangler": "^4.30.0", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@shadcn/ui": "^0.0.4", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5.7.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}