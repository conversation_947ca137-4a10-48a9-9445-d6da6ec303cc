import { queryOptions } from "@tanstack/react-query";
import { getUserId, getUserSession } from "~/lib/auth/auth-serverFn";

const user = () => {
	return queryOptions({
		queryKey: [...authQueries.all, "user"],
		queryFn: getUserSession,
		staleTime: 5000,
	});
};

const userId = () => {
	return queryOptions({
		queryKey: [...authQueries.all, "userId"],
		queryFn: getUserId,
		staleTime: 5000,
	});
};

export const authQueries = {
	all: ["auth"],
	user,
	userId,
};
