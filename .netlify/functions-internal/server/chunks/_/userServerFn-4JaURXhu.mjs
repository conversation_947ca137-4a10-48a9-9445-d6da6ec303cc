import { eq } from 'drizzle-orm';
import * as v$1 from 'valibot';
import { d as createServerRpc, b as createServerFn, e as db, u as user } from './ssr.mjs';
import 'react/jsx-runtime';
import '@tanstack/react-router';
import '@tanstack/react-query';
import 'react';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'sonner';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'uuid';
import '@tanstack/react-router/ssr/server';

const getUserByEmail_createServerFn_handler = createServerRpc("src_serverFn_userServerFn_ts--getUserByEmail_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getUserByEmail.__executeServer(opts, signal);
});
const deleteUser_createServerFn_handler = createServerRpc("src_serverFn_userServerFn_ts--deleteUser_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return deleteUser.__executeServer(opts, signal);
});
const getUserByEmail = createServerFn({
  method: "GET"
}).validator(v$1.string()).handler(getUserByEmail_createServerFn_handler, async ({
  data
}) => {
  try {
    const existedUser = await db.select().from(user).where(eq(user.email, data));
    if (existedUser.length === 0) {
      return null;
    }
    return existedUser[0];
  } catch (error) {
    console.error("Failed to read user by email:", error);
    return null;
  }
});
const deleteUser = createServerFn({
  method: "POST"
}).validator(v$1.string()).handler(deleteUser_createServerFn_handler, async ({
  data
}) => {
  try {
    await db.delete(user).where(eq(user.email, data));
    return true;
  } catch (error) {
    console.error("Failed to delete user:", error);
    return false;
  }
});

export { deleteUser_createServerFn_handler, getUserByEmail_createServerFn_handler };
//# sourceMappingURL=userServerFn-4JaURXhu.mjs.map
