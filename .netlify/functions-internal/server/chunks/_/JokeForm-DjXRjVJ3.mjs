import { jsx, jsxs } from 'react/jsx-runtime';
import { Suspense } from 'react';
import { toast } from 'sonner';
import { u as useAppForm } from './hook-BE_X5b1f.mjs';
import { j as jokeCollection } from './collections-D-7c4uAG.mjs';
import { j as addJokeSchema } from './ssr.mjs';

function JokeForm({ joke }) {
  const label = joke ? "Update Joke" : "Add Joke";
  const operation = joke ? "update" : "add";
  const form = useAppForm({
    defaultValues: {
      question: (joke == null ? void 0 : joke.question) || "",
      answer: (joke == null ? void 0 : joke.answer) || ""
    },
    validators: {
      onChange: addJokeSchema
    },
    onSubmit: async ({ value }) => {
      try {
        if (joke) {
          const updatedValue = {
            ...value,
            id: joke.id
          };
          jokeCollection.update(updatedValue.id, (draft) => {
            draft.question = updatedValue.question;
            draft.answer = updatedValue.answer;
            form.reset({
              question: draft.question,
              answer: draft.answer
            });
          });
        } else {
          const newValue = {
            ...value,
            id: ""
          };
          jokeCollection.insert(newValue);
        }
        toast.success(`Joke ${operation} successfully.`);
        form.reset();
      } catch (error) {
        console.error(`Failed to ${operation} joke:`, error);
        toast.error(`Failed to ${operation} joke. Please try again.`);
      } finally {
      }
    }
  });
  return /* @__PURE__ */ jsx(
    Suspense,
    {
      fallback: /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center p-8", children: /* @__PURE__ */ jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary" }) }),
      children: /* @__PURE__ */ jsx("div", { className: "max-w-2xl mx-auto p-6", children: /* @__PURE__ */ jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border/50 overflow-hidden", children: [
        /* @__PURE__ */ jsx("div", { className: "bg-gradient-to-r from-primary/10 to-secondary/10 px-8 py-6 border-b border-border/50", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
          /* @__PURE__ */ jsx("div", { className: "text-3xl", children: "\u{1F3AD}" }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold tracking-tight text-card-foreground", children: joke ? "Edit Your Joke" : "Create a New Joke" }),
            /* @__PURE__ */ jsx("p", { className: "text-muted-foreground mt-1", children: joke ? "Update your hilarious joke" : "Share your best jokes with the community" })
          ] })
        ] }) }),
        /* @__PURE__ */ jsxs(
          "form",
          {
            className: "p-8 space-y-6",
            onSubmit: (e) => {
              e.preventDefault();
              form.handleSubmit();
            },
            children: [
              /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
                /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
                  /* @__PURE__ */ jsxs("label", { className: "text-sm font-medium text-card-foreground flex items-center gap-2", children: [
                    /* @__PURE__ */ jsx("span", { className: "text-lg", children: "\u2753" }),
                    "Setup (Question)"
                  ] }),
                  /* @__PURE__ */ jsx(form.AppField, { name: "question", children: (field) => /* @__PURE__ */ jsx(
                    field.TextField,
                    {
                      label: "",
                      placeholder: "What's the setup for your joke?"
                    }
                  ) })
                ] }),
                /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
                  /* @__PURE__ */ jsxs("label", { className: "text-sm font-medium text-card-foreground flex items-center gap-2", children: [
                    /* @__PURE__ */ jsx("span", { className: "text-lg", children: "\u{1F602}" }),
                    "Punchline (Answer)"
                  ] }),
                  /* @__PURE__ */ jsx(form.AppField, { name: "answer", children: (field) => /* @__PURE__ */ jsx(
                    field.TextField,
                    {
                      label: "",
                      placeholder: "What's the hilarious punchline?"
                    }
                  ) })
                ] })
              ] }),
              /* @__PURE__ */ jsx(form.AppForm, { children: /* @__PURE__ */ jsx("div", { className: "flex justify-end pt-4 border-t border-border/50", children: /* @__PURE__ */ jsx(form.SubscribeButton, { label: `\u2728 ${label}`, variant: "default" }) }) })
            ]
          }
        )
      ] }) })
    }
  );
}

export { JokeForm as J };
//# sourceMappingURL=JokeForm-DjXRjVJ3.mjs.map
