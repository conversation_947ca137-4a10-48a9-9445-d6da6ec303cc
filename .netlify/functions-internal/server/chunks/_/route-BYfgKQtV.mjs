import { jsxs, jsx } from 'react/jsx-runtime';
import { Link, Outlet, useRouter } from '@tanstack/react-router';
import * as React from 'react';
import { useState, useEffect } from 'react';
import { useLiveQuery } from '@tanstack/react-db';
import { ArrowUpDown } from 'lucide-react';
import { B as Button, c as cn } from './ssr.mjs';
import { j as jokeCollection } from './collections-D-7c4uAG.mjs';
import { useReactTable, getFilteredRowModel, getSortedRowModel, getPaginationRowModel, getCoreRowModel, flexRender } from '@tanstack/react-table';
import { I as Input } from './input-Cp6Zj0xY.mjs';
import '@tanstack/react-query';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'sonner';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'valibot';
import 'drizzle-orm';
import 'uuid';
import '@tanstack/react-router/ssr/server';
import '@tanstack/query-db-collection';

function TableButton({ joke }) {
  const router = useRouter();
  return /* @__PURE__ */ jsxs("div", { className: "flex flex-row gap-2 items-center", children: [
    /* @__PURE__ */ jsx(Button, { asChild: true, className: "bg-blue-500", size: "sm", children: /* @__PURE__ */ jsx(Link, { to: "/joke-table/update", search: { id: joke.id }, children: "Update" }) }),
    /* @__PURE__ */ jsx(
      Button,
      {
        onClick: () => {
          const result = confirm("Are you sure you want to delete this joke?");
          if (result) {
            jokeCollection.delete(joke.id);
            router.navigate({
              to: "/joke-table"
            });
          }
        },
        variant: "destructive",
        size: "sm",
        children: "Delete"
      }
    )
  ] });
}
const columns = [
  {
    accessorKey: "id",
    header: ({ column }) => {
      return /* @__PURE__ */ jsxs(
        Button,
        {
          variant: "ghost",
          onClick: () => column.toggleSorting(column.getIsSorted() === "asc"),
          children: [
            "Id",
            /* @__PURE__ */ jsx(ArrowUpDown, {})
          ]
        }
      );
    },
    cell: ({ row }) => /* @__PURE__ */ jsx("div", { className: "lowercase", children: row.getValue("id") })
  },
  {
    accessorKey: "question",
    header: () => /* @__PURE__ */ jsx("div", { className: "text-left", children: "Question" })
  },
  {
    accessorKey: "answer",
    header: () => /* @__PURE__ */ jsx("div", { className: "text-left", children: "Answer" })
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const joke = row.original;
      return /* @__PURE__ */ jsx(TableButton, { joke });
    }
  }
];
function Table({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "table-container",
      className: "relative w-full overflow-x-auto",
      children: /* @__PURE__ */ jsx(
        "table",
        {
          "data-slot": "table",
          className: cn("w-full caption-bottom text-sm", className),
          ...props
        }
      )
    }
  );
}
function TableHeader({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "thead",
    {
      "data-slot": "table-header",
      className: cn("[&_tr]:border-b", className),
      ...props
    }
  );
}
function TableBody({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "tbody",
    {
      "data-slot": "table-body",
      className: cn("[&_tr:last-child]:border-0", className),
      ...props
    }
  );
}
function TableRow({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "tr",
    {
      "data-slot": "table-row",
      className: cn(
        "hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",
        className
      ),
      ...props
    }
  );
}
function TableHead({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "th",
    {
      "data-slot": "table-head",
      className: cn(
        "text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        className
      ),
      ...props
    }
  );
}
function TableCell({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "td",
    {
      "data-slot": "table-cell",
      className: cn(
        "p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        className
      ),
      ...props
    }
  );
}
function DataTable({
  columns: columns2,
  data
}) {
  var _a2;
  var _a, _b;
  const [sorting, setSorting] = React.useState([]);
  const [columnFilters, setColumnFilters] = React.useState(
    []
  );
  const [columnVisibility, setColumnVisibility] = React.useState({});
  const [rowSelection, setRowSelection] = React.useState({});
  const table = useReactTable({
    data,
    columns: columns2,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection
    }
  });
  return /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
    /* @__PURE__ */ jsx("div", { className: "flex items-center py-4", children: /* @__PURE__ */ jsx(
      Input,
      {
        placeholder: "Filter id...",
        value: (_a2 = (_a = table.getColumn("id")) == null ? void 0 : _a.getFilterValue()) != null ? _a2 : "",
        onChange: (event) => {
          var _a22;
          return (_a22 = table.getColumn("id")) == null ? void 0 : _a22.setFilterValue(event.target.value);
        },
        className: "max-w-sm"
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: "overflow-hidden rounded-md border", children: /* @__PURE__ */ jsxs(Table, { children: [
      /* @__PURE__ */ jsx(TableHeader, { children: table.getHeaderGroups().map((headerGroup) => /* @__PURE__ */ jsx(TableRow, { children: headerGroup.headers.map((header) => {
        return /* @__PURE__ */ jsx(TableHead, { children: header.isPlaceholder ? null : flexRender(
          header.column.columnDef.header,
          header.getContext()
        ) }, header.id);
      }) }, headerGroup.id)) }),
      /* @__PURE__ */ jsx(TableBody, { children: ((_b = table.getRowModel().rows) == null ? void 0 : _b.length) ? table.getRowModel().rows.map((row) => /* @__PURE__ */ jsx(
        TableRow,
        {
          "data-state": row.getIsSelected() && "selected",
          children: row.getVisibleCells().map((cell) => /* @__PURE__ */ jsx(TableCell, { children: flexRender(
            cell.column.columnDef.cell,
            cell.getContext()
          ) }, cell.id))
        },
        row.id
      )) : /* @__PURE__ */ jsx(TableRow, { children: /* @__PURE__ */ jsx(
        TableCell,
        {
          colSpan: columns2.length,
          className: "h-24 text-center",
          children: "No results."
        }
      ) }) })
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-end space-x-2 py-4", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-muted-foreground flex-1 text-sm", children: [
        table.getFilteredSelectedRowModel().rows.length,
        " of",
        " ",
        table.getFilteredRowModel().rows.length,
        " row(s) selected."
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "space-x-2", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            variant: "outline",
            size: "sm",
            onClick: () => table.previousPage(),
            disabled: !table.getCanPreviousPage(),
            children: "Previous"
          }
        ),
        /* @__PURE__ */ jsx(
          Button,
          {
            variant: "outline",
            size: "sm",
            onClick: () => table.nextPage(),
            disabled: !table.getCanNextPage(),
            children: "Next"
          }
        )
      ] })
    ] })
  ] });
}
function JokeTable() {
  const { data: jokes, isLoading } = useLiveQuery(
    (q) => q.from({ joke: jokeCollection })
  );
  if (isLoading) {
    return /* @__PURE__ */ jsx("div", { className: "text-center", children: "Loading..." });
  }
  return /* @__PURE__ */ jsx("div", { className: "flex flex-col gap-8 justify-center", children: /* @__PURE__ */ jsx(DataTable, { columns, data: jokes }) });
}
const SplitComponent = function RouteComponent() {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  if (isClient) {
    return /* @__PURE__ */ jsxs("div", { className: "container mx-auto py-10", children: [
      /* @__PURE__ */ jsx(Button, { className: "bg-pink-500", children: /* @__PURE__ */ jsx(Link, { to: "/joke-table/new", children: "new" }) }),
      /* @__PURE__ */ jsx(JokeTable, {}),
      /* @__PURE__ */ jsx(Outlet, {})
    ] });
  }
};

export { SplitComponent as component };
//# sourceMappingURL=route-BYfgKQtV.mjs.map
