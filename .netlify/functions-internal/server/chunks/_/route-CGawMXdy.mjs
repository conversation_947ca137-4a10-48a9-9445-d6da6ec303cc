import { jsx, jsxs } from 'react/jsx-runtime';
import { Outlet, Link } from '@tanstack/react-router';
import { useState, useEffect } from 'react';
import { useLiveQuery } from '@tanstack/react-db';
import { j as jokeCollection } from './collections-D-7c4uAG.mjs';
import '@tanstack/query-db-collection';
import './ssr.mjs';
import '@tanstack/react-query';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'sonner';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'valibot';
import 'drizzle-orm';
import 'uuid';
import '@tanstack/react-router/ssr/server';

function JokesList() {
  const {
    data: jokes,
    isLoading,
    isError
  } = useLiveQuery((q) => q.from({ joke: jokeCollection }));
  if (isLoading) {
    return /* @__PURE__ */ jsx("div", { className: "container mx-auto py-6 px-4", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center items-center h-64", children: /* @__PURE__ */ jsx("p", { children: "Loading jokes..." }) }) });
  }
  if (isError) {
    return /* @__PURE__ */ jsx("div", { className: "container mx-auto py-6 px-4", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center items-center h-64", children: /* @__PURE__ */ jsx("p", { className: "text-red-500", children: "Error loading jokes" }) }) });
  }
  if (!jokes || jokes.length === 0) {
    return /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center py-16 px-4", children: [
      /* @__PURE__ */ jsx("div", { className: "text-6xl mb-6", children: "\u{1F605}" }),
      /* @__PURE__ */ jsx("h3", { className: "text-xl font-semibold text-muted-foreground mb-2", children: "No jokes yet!" }),
      /* @__PURE__ */ jsx("p", { className: "text-muted-foreground mb-6 text-center max-w-md", children: "Looks like the comedy club is empty. Be the first to share a joke!" })
    ] });
  }
  return /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsx("div", { className: "flex justify-between items-center", children: /* @__PURE__ */ jsxs("div", { children: [
      /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold tracking-tight", children: "Jokes Collection" }),
      /* @__PURE__ */ jsx("p", { className: "text-muted-foreground", children: "Discover hilarious jokes from our community" })
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "flex flex-col gap-2 justify-center", children: jokes.map((joke, index) => /* @__PURE__ */ jsx(
      Link,
      {
        to: `/jokes/$id`,
        params: (prev) => ({ ...prev, id: joke.id }),
        className: "group block bg-card border border-border rounded-lg p-6 hover:shadow-lg transition-all duration-200 hover:scale-[1.02] hover:border-primary/50",
        style: { animationDelay: `${index * 100}ms` },
        children: /* @__PURE__ */ jsxs("div", { className: "flex items-start gap-3", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl", children: "\u{1F602}" }),
          /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsx("p", { className: "font-semibold text-card-foreground group-hover:text-primary transition-colors line-clamp-2", children: joke.question }),
            /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground mt-2", children: "Click to see the punchline!" })
          ] })
        ] })
      },
      joke.id
    )) })
  ] });
}
const SplitComponent = function App() {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  return /* @__PURE__ */ jsx("div", { className: "container mx-auto py-6 px-4", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
    /* @__PURE__ */ jsx("div", { className: "w-full", children: /* @__PURE__ */ jsx("div", { className: "bg-white rounded-lg shadow-sm p-6", children: isClient && /* @__PURE__ */ jsx(JokesList, {}) }) }),
    /* @__PURE__ */ jsx("div", { className: "w-full", children: /* @__PURE__ */ jsx(Outlet, {}) })
  ] }) });
};

export { SplitComponent as component };
//# sourceMappingURL=route-CGawMXdy.mjs.map
