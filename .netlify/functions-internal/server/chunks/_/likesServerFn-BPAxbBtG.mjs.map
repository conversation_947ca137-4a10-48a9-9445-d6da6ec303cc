{"version": 3, "file": "likesServerFn-BPAxbBtG.mjs", "sources": ["../../../../../../src/serverFn/likesServerFn.ts?tsr-directive-use-server="], "sourcesContent": null, "names": ["getLikedJokesByUser_createServerFn_handler", "createServerRpc", "opts", "signal", "getLikedJokesByUser.__executeServer", "createLikedJoke_createServerFn_handler", "createLikedJoke.__executeServer", "unlikeJoke_createServerFn_handler", "unlikeJoke.__executeServer", "getLikedJokesByUser", "createServerFn", "method", "validator", "v", "string", "handler", "data", "db", "select", "from", "liked", "where", "eq", "userId", "error", "console", "createLikedJoke", "likeJokeSchema", "existing", "and", "jokeId", "length", "success", "message", "insert", "values", "id", "uuidv4", "unlikeJoke", "delete"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAOuD,MAAAA,6CAAAC,eAAAA,CAAA,2EAAA,EAAA,YAAA,EAM9C,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAC,mBAAAA,CAAAA,eAAAA,CAAAF,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAAA,MAAAE,yCAAAJ,eAAAA,CAAA,uEAAA,EAAA,YAAA,EAaA,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAG,eAAAA,CAAAA,eAAAA,CAAAJ,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AA6BT,MAAAI,oCAAAN,eAAAA,CAAA,kEAAA,EAAA,YAAA,EAKS,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAK,UAAAA,CAAAA,eAAAA,CAAAN,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAnDF,MAAMM,sBAAsBC,cAAAA,CAAe;AAAA,EACjDC,MAAAA,EAAQ;AACT,CAAC,CAAA,CACCC,UAAUC,GAAAA,CAAEC,MAAAA,EAAQ,CAAA,CACpBC,OAAAA,CAAOf,4CAAC,OAAO;AAAA,EAAEgB;AAAuB,CAAA,KAAM;AAC9C,EAAA,IAAI;AACH,IAAA,OAAO,MAAMC,EAAAA,CAAGC,MAAAA,EAAAA,CAASC,IAAAA,CAAKC,KAAK,CAAA,CAAEC,KAAAA,CAAMC,EAAAA,CAAGF,KAAAA,CAAMG,MAAAA,EAAQP,IAAI,CAAC,CAAA;AAAA,EAClE,SAASQ,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,8BAA8BA,KAAK,CAAA;AACjD,IAAA,OAAO,EAAA;AAAA,EACR;AACD,CAAC,CAAA;AAEK,MAAME,kBAAkBhB,cAAAA,CAAe;AAAA,EAC7CC,MAAAA,EAAQ;AACT,CAAC,EACCC,SAAAA,CAAUe,cAAc,CAAA,CACxBZ,OAAAA,CAAOV,wCAAC,OAAO;AAAA,EAAEW;AAA8B,CAAA,KAAM;AACrD,EAAA,IAAI;AAEH,IAAA,MAAMY,QAAAA,GAAW,MAAMX,EAAAA,CACrBC,MAAAA,GACAC,IAAAA,CAAKC,KAAK,CAAA,CACVC,KAAAA,CACAQ,GAAAA,CAAIP,EAAAA,CAAGF,MAAMU,MAAAA,EAAQd,IAAAA,CAAKc,MAAM,CAAA,EAAGR,EAAAA,CAAGF,MAAMG,MAAAA,EAAQP,IAAAA,CAAKO,MAAM,CAAC,CACjE,CAAA;AAED,IAAA,IAAIK,QAAAA,CAASG,SAAS,CAAA,EAAG;AACxBN,MAAAA,OAAAA,CAAQD,MAAM,uBAAuB,CAAA;AACrC,MAAA,OAAO;AAAA,QAAEQ,OAAAA,EAAS,KAAA;AAAA,QAAOC,OAAAA,EAAS;AAAA,OAAA;AAAA,IACnC;AAGA,IAAA,MAAMhB,EAAAA,CAAGiB,MAAAA,CAAOd,KAAK,CAAA,CAAEe,MAAAA,CAAO;AAAA,MAC7BC,IAAIC,EAAAA,EAAAA;AAAAA,MACJP,QAAQd,IAAAA,CAAKc,MAAAA;AAAAA,MACbP,QAAQP,IAAAA,CAAKO;AAAAA,KACb,CAAA;AAED,IAAA,OAAO;AAAA,MAAES,OAAAA,EAAS,IAAA;AAAA,MAAMC,OAAAA,EAAS;AAAA,KAAA;AAAA,EAClC,SAAST,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,wBAAwBA,KAAK,CAAA;AAC3C,IAAA,OAAO;AAAA,MAAEQ,OAAAA,EAAS,KAAA;AAAA,MAAOC,OAAAA,EAAS;AAAA,KAAA;AAAA,EACnC;AACD,CAAC,CAAA;AAGK,MAAMK,aAAa5B,cAAAA,CAAe;AAAA,EACxCC,MAAAA,EAAQ;AACT,CAAC,EACCC,SAAAA,CAAUe,cAAc,CAAA,CACxBZ,OAAAA,CAAOR,mCAAC,OAAO;AAAA,EAAES;AAA8B,CAAA,KAAM;AACrD,EAAA,IAAI;AACH,IAAA,MAAMC,GACJsB,MAAAA,CAAOnB,KAAK,EACZC,KAAAA,CACAQ,GAAAA,CAAIP,GAAGF,KAAAA,CAAMU,MAAAA,EAAQd,IAAAA,CAAKc,MAAM,GAAGR,EAAAA,CAAGF,KAAAA,CAAMG,QAAQP,IAAAA,CAAKO,MAAM,CAAC,CACjE,CAAA;AAED,IAAA,OAAO;AAAA,MAAES,OAAAA,EAAS,IAAA;AAAA,MAAMC,OAAAA,EAAS;AAAA,KAAA;AAAA,EAClC,SAAST,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,0BAA0BA,KAAK,CAAA;AAC7C,IAAA,OAAO;AAAA,MAAEQ,OAAAA,EAAS,KAAA;AAAA,MAAOC,OAAAA,EAAS;AAAA,KAAA;AAAA,EACnC;AACD,CAAC,CAAA;;;;"}