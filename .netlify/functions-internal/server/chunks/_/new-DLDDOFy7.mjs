import { jsx } from 'react/jsx-runtime';
import { J as JokeForm } from './JokeForm-DjXRjVJ3.mjs';
import 'react';
import 'sonner';
import './hook-BE_X5b1f.mjs';
import '@tanstack/form-core';
import '@tanstack/react-store';
import './ssr.mjs';
import '@tanstack/react-router';
import '@tanstack/react-query';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'valibot';
import 'drizzle-orm';
import 'uuid';
import '@tanstack/react-router/ssr/server';
import 'lucide-react';
import './input-Cp6Zj0xY.mjs';
import './collections-D-7c4uAG.mjs';
import '@tanstack/query-db-collection';
import '@tanstack/react-db';

const SplitComponent = function RouteComponent() {
  return /* @__PURE__ */ jsx(JokeForm, {});
};

export { SplitComponent as component };
//# sourceMappingURL=new-DLDDOFy7.mjs.map
