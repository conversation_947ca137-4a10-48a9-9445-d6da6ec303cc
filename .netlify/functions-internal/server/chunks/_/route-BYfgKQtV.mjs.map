{"version": 3, "file": "route-BYfgKQtV.mjs", "sources": ["../../../../../../src/components/TableButton.tsx", "../../../../../../src/components/table/columns.tsx", "../../../../../../src/components/ui/table.tsx", "../../../../../../src/components/table/data-table.tsx", "../../../../../../src/components/JokeTable.tsx", "../../../../../../src/routes/joke-table/route.tsx?tsr-split=component"], "sourcesContent": null, "names": ["columns", "_a", "SplitComponent", "RouteComponent", "isClient", "setIsClient", "useState", "useEffect"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAwB,WAAA,CAAY,EAAE,IAAA,EAAA,EAAqB;AAC1D,EAAA,MAAM,SAAS,SAAA,EAAA;AAEf,EAAA,uBACC,IAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,oCACd,QAAA,EAAA;AAAA,oBAAA,GAAA,CAAC,MAAA,IAAO,OAAA,EAAO,IAAA,EAAC,WAAW,aAAA,EAAe,IAAA,EAAK,IAAA,EAC9C,QAAA,kBAAA,GAAA,CAAC,IAAA,EAAA,EAAK,EAAA,EAAG,oBAAA,EAAqB,MAAA,EAAQ,EAAE,EAAA,EAAI,IAAA,CAAK,EAAA,EAAA,EAAM,QAAA,EAAA,QAAA,EAEvD,CAAA,EACD,CAAA;AAAA,oBAEA,GAAA;AAAA,MAAC,MAAA;AAAA,MAAA;AAAA,QACA,SAAS,MAAM;AACd,UAAA,MAAM,MAAA,GAAS,QAAQ,4CAA4C,CAAA;AACnE,UAAA,IAAI,MAAA,EAAQ;AACX,YAAA,cAAA,CAAe,MAAA,CAAO,KAAK,EAAE,CAAA;AAE7B,YAAA,MAAA,CAAO,QAAA,CAAS;AAAA,cACf,EAAA,EAAI;AAAA,aACJ,CAAA;AAAA,UACF;AAAA,QACD,CAAA;AAAA,QACA,OAAA,EAAQ,aAAA;AAAA,QACR,IAAA,EAAK,IAAA;AAAA,QACL,QAAA,EAAA;AAAA;AAAA;AAAA,KAGF,CAAA;AAEF;AChCO,MAAM,OAAA,GAAmC;AAAA,EAC/C;AAAA,IACC,WAAA,EAAa,IAAA;AAAA,IACb,MAAA,EAAQ,CAAC,EAAE,MAAA,EAAA,KAAa;AACvB,MAAA,uBACC,IAAA;AAAA,QAAC,MAAA;AAAA,QAAA;AAAA,UACA,OAAA,EAAQ,OAAA;AAAA,UACR,SAAS,MAAM,MAAA,CAAO,cAAc,MAAA,CAAO,WAAA,OAAkB,KAAK,CAAA;AAAA,UAClE,QAAA,EAAA;AAAA,YAAA,IAAA;AAAA,4BAAA,GAAA,CAEC,WAAA,EAAA,EAAY;AAAA;AAAA;AAAA,OAAA;AAAA,IAGhB,CAAA;AAAA,IACA,IAAA,EAAM,CAAC,EAAE,GAAA,2BAAW,KAAA,EAAA,EAAI,SAAA,EAAU,aAAa,QAAA,EAAA,GAAA,CAAI,QAAA,CAAS,IAAI,GAAE;AAAA,GAAA;AAAA,EAEnE;AAAA,IACC,WAAA,EAAa,UAAA;AAAA,IACb,MAAA,EAAQ,sBAAM,GAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,WAAA,EAAY,QAAA,EAAA,UAAA,EAAQ;AAAA,GAAA;AAAA,EAElD;AAAA,IACC,WAAA,EAAa,QAAA;AAAA,IACb,MAAA,EAAQ,sBAAM,GAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,WAAA,EAAY,QAAA,EAAA,QAAA,EAAM;AAAA,GAAA;AAAA,EAEhD;AAAA,IACC,EAAA,EAAI,SAAA;AAAA,IACJ,YAAA,EAAc,KAAA;AAAA,IACd,IAAA,EAAM,CAAC,EAAE,GAAA,EAAA,KAAU;AAClB,MAAA,MAAM,OAAO,GAAA,CAAI,QAAA;AAEjB,MAAA,uBAAO,GAAA,CAAC,WAAA,IAAY,IAAA,EAAY,CAAA;AAAA,IACjC;AAAA;AAEF,CAAA;ACnCA,SAAS,KAAA,CAAM,EAAE,SAAA,EAAW,GAAG,OAAA,EAAwC;AACrE,EAAA,uBACE,GAAA;AAAA,IAAC,KAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAU,iBAAA;AAAA,MACV,SAAA,EAAU,iCAAA;AAAA,MAEV,QAAA,kBAAA,GAAA;AAAA,QAAC,OAAA;AAAA,QAAA;AAAA,UACC,WAAA,EAAU,OAAA;AAAA,UACV,SAAA,EAAW,EAAA,CAAG,+BAAA,EAAiC,SAAS,CAAA;AAAA,UACvD,GAAG;AAAA;AAAA;AAAA;AACN,GAAA;AAGN;AAEA,SAAS,WAAA,CAAY,EAAE,SAAA,EAAW,GAAG,OAAA,EAAwC;AAC3E,EAAA,uBACE,GAAA;AAAA,IAAC,OAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAU,cAAA;AAAA,MACV,SAAA,EAAW,EAAA,CAAG,iBAAA,EAAmB,SAAS,CAAA;AAAA,MACzC,GAAG;AAAA;AAAA,GAAA;AAGV;AAEA,SAAS,SAAA,CAAU,EAAE,SAAA,EAAW,GAAG,OAAA,EAAwC;AACzE,EAAA,uBACE,GAAA;AAAA,IAAC,OAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAU,YAAA;AAAA,MACV,SAAA,EAAW,EAAA,CAAG,4BAAA,EAA8B,SAAS,CAAA;AAAA,MACpD,GAAG;AAAA;AAAA,GAAA;AAGV;AAeA,SAAS,QAAA,CAAS,EAAE,SAAA,EAAW,GAAG,OAAA,EAAqC;AACrE,EAAA,uBACE,GAAA;AAAA,IAAC,IAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAU,WAAA;AAAA,MACV,SAAA,EAAW,EAAA;AAAA,QACT,6EAAA;AAAA,QACA;AAAA,OAAA;AAAA,MAED,GAAG;AAAA;AAAA,GAAA;AAGV;AAEA,SAAS,SAAA,CAAU,EAAE,SAAA,EAAW,GAAG,OAAA,EAAqC;AACtE,EAAA,uBACE,GAAA;AAAA,IAAC,IAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAU,YAAA;AAAA,MACV,SAAA,EAAW,EAAA;AAAA,QACT,oJAAA;AAAA,QACA;AAAA,OAAA;AAAA,MAED,GAAG;AAAA;AAAA,GAAA;AAGV;AAEA,SAAS,SAAA,CAAU,EAAE,SAAA,EAAW,GAAG,OAAA,EAAqC;AACtE,EAAA,uBACE,GAAA;AAAA,IAAC,IAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAU,YAAA;AAAA,MACV,SAAA,EAAW,EAAA;AAAA,QACT,wGAAA;AAAA,QACA;AAAA,OAAA;AAAA,MAED,GAAG;AAAA;AAAA,GAAA;AAGV;AC5DO,SAAS,SAAA,CAAyB;AAAA,EACxC,OAAA,EAAAA,QAAAA;AAAAA,EACA;AACD,CAAA,EAAkC;;;AACjC,EAAA,MAAM,CAAC,OAAA,EAAS,UAAU,IAAI,KAAA,CAAM,QAAA,CAAuB,EAAE,CAAA;AAC7D,EAAA,MAAM,CAAC,aAAA,EAAe,gBAAgB,CAAA,GAAI,KAAA,CAAM,QAAA;AAAA,IAC/C;AAAA,GAAC;AAEF,EAAA,MAAM,CAAC,gBAAA,EAAkB,mBAAmB,IAC3C,KAAA,CAAM,QAAA,CAA0B,EAAE,CAAA;AACnC,EAAA,MAAM,CAAC,YAAA,EAAc,eAAe,IAAI,KAAA,CAAM,QAAA,CAAS,EAAE,CAAA;AAEzD,EAAA,MAAM,QAAQ,aAAA,CAAc;AAAA,IAC3B,IAAA;AAAA,IACA,OAAA,EAAAA,QAAAA;AAAAA,IACA,eAAA,EAAiB,UAAA;AAAA,IACjB,qBAAA,EAAuB,gBAAA;AAAA,IACvB,iBAAiB,eAAA,EAAA;AAAA,IACjB,uBAAuB,qBAAA,EAAA;AAAA,IACvB,mBAAmB,iBAAA,EAAA;AAAA,IACnB,qBAAqB,mBAAA,EAAA;AAAA,IACrB,wBAAA,EAA0B,mBAAA;AAAA,IAC1B,oBAAA,EAAsB,eAAA;AAAA,IACtB,KAAA,EAAO;AAAA,MACN,OAAA;AAAA,MACA,aAAA;AAAA,MACA,gBAAA;AAAA,MACA;AAAA;AAAA,GAED,CAAA;AAED,EAAA,uBACC,IAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,UACd,QAAA,EAAA;AAAA,oBAAA,GAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,0BACd,QAAA,kBAAA,GAAA;AAAA,MAAC,KAAA;AAAA,MAAA;AAAA,QACA,WAAA,EAAY,cAAA;AAAA,QACZ,KAAA,EAAA,CAAQC,GAAAA,GAAAA,CAAA,EAAA,GAAA,KAAA,CAAM,SAAA,CAAU,IAAI,CAAA,KAApB,IAAA,eAAuB,cAAA,EAAA,KAAvB,OAAAA,GAAAA,GAAsD,EAAA;AAAA,QAC9D,QAAA,EAAU,CAAC,KAAA,KAAA;;AACV,UAAA,OAAA,CAAAA,IAAAA,GAAA,KAAA,CAAM,SAAA,CAAU,IAAI,CAAA,KAApB,IAAA,GAAA,MAAA,GAAAA,IAAAA,CAAuB,cAAA,CAAe,KAAA,CAAM,MAAA,CAAO,KAAA,CAAA;AAAA,QAAA,CAAA;AAAA,QAEpD,SAAA,EAAU;AAAA;AAAA,OAEZ,CAAA;AAAA,oBACA,GAAA,CAAC,OAAA,EAAI,SAAA,EAAU,qCACd,QAAA,kBAAA,IAAA,CAAC,KAAA,EAAA,EACA,QAAA,EAAA;AAAA,0BAAC,WAAA,EAAA,EACC,UAAA,KAAA,CAAM,eAAA,GAAkB,GAAA,CAAI,CAAC,WAAA,qBAC7B,GAAA,CAAC,UAAA,EACC,QAAA,EAAA,YAAY,OAAA,CAAQ,GAAA,CAAI,CAAC,MAAA,KAAW;AACpC,QAAA,2BACE,SAAA,EAAA,EACC,QAAA,EAAA,MAAA,CAAO,gBACL,IAAA,GACA,UAAA;AAAA,UACA,MAAA,CAAO,OAAO,SAAA,CAAU,MAAA;AAAA,UACxB,OAAO,UAAA;AAAA,SAAW,EACnB,EANa,MAAA,CAAO,EAOvB,CAAA;AAAA,MAEF,CAAC,CAAA,EAAA,EAZa,YAAY,EAa3B,CACA,GACF,CAAA;AAAA,0BACC,SAAA,EAAA,EACC,YAAA,EAAA,GAAA,KAAA,CAAM,aAAA,CAAc,IAAA,KAApB,OAAA,MAAA,GAAA,EAAA,CAA0B,UAC1B,KAAA,CAAM,WAAA,GAAc,IAAA,CAAK,GAAA,CAAI,CAAC,GAAA,qBAC7B,GAAA;AAAA,QAAC,QAAA;AAAA,QAAA;AAAA,UAEA,YAAA,EAAY,GAAA,CAAI,aAAA,EAAA,IAAmB,UAAA;AAAA,UAElC,QAAA,EAAA,GAAA,CAAI,eAAA,EAAA,CAAkB,GAAA,CAAI,CAAC,IAAA,qBAAA,GAAA,CAC1B,SAAA,EAAA,EACC,QAAA,EAAA,UAAA;AAAA,YACA,IAAA,CAAK,OAAO,SAAA,CAAU,IAAA;AAAA,YACtB,KAAK,UAAA;AAAA,WAAW,EACjB,EAJe,IAAA,CAAK,EAKrB,CACA;AAAA,SAAA;AAAA,QAVI,GAAA,CAAI;AAAA,OAYV,CAAA,mBAED,GAAA,CAAC,QAAA,EAAA,EACA,QAAA,kBAAA,GAAA;AAAA,QAAC,SAAA;AAAA,QAAA;AAAA,UACA,SAASD,QAAAA,CAAQ,MAAA;AAAA,UACjB,SAAA,EAAU,kBAAA;AAAA,UACV,QAAA,EAAA;AAAA;AAAA,OAAA,EAGF,CAAA,EAEF;AAAA,KAAA,EACD,CAAA,EACD,CAAA;AAAA,oBACA,IAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,gDACd,QAAA,EAAA;AAAA,sBAAA,IAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,wCACb,QAAA,EAAA;AAAA,QAAA,KAAA,CAAM,2BAAA,GAA8B,IAAA,CAAK,MAAA;AAAA,QAAO,KAAA;AAAA,QAAI,GAAA;AAAA,QACpD,KAAA,CAAM,mBAAA,GAAsB,IAAA,CAAK,MAAA;AAAA,QAAO;AAAA,SAC1C,CAAA;AAAA,sBACA,IAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,aACd,QAAA,EAAA;AAAA,wBAAA,GAAA;AAAA,UAAC,MAAA;AAAA,UAAA;AAAA,YACA,OAAA,EAAQ,SAAA;AAAA,YACR,IAAA,EAAK,IAAA;AAAA,YACL,OAAA,EAAS,MAAM,KAAA,CAAM,YAAA,EAAA;AAAA,YACrB,QAAA,EAAU,CAAC,KAAA,CAAM,kBAAA,EAAA;AAAA,YACjB,QAAA,EAAA;AAAA;AAAA,SAAA;AAAA,wBAGD,GAAA;AAAA,UAAC,MAAA;AAAA,UAAA;AAAA,YACA,OAAA,EAAQ,SAAA;AAAA,YACR,IAAA,EAAK,IAAA;AAAA,YACL,OAAA,EAAS,MAAM,KAAA,CAAM,QAAA,EAAA;AAAA,YACrB,QAAA,EAAU,CAAC,KAAA,CAAM,cAAA,EAAA;AAAA,YACjB,QAAA,EAAA;AAAA;AAAA;AAAA,SAGF;AAAA,OACD;AAAA,KACD,CAAA;AAEF;AC/IA,SAAwB,SAAA,GAAY;AACnC,EAAA,MAAM,EAAE,IAAA,EAAM,KAAA,EAAO,SAAA,EAAA,GAAc,YAAA;AAAA,IAAa,CAAC,CAAA,KAChD,CAAA,CAAE,KAAK,EAAE,IAAA,EAAM,gBAAgB;AAAA,GAAA;AAGhC,EAAA,IAAI,SAAA,EAAW;AACd,IAAA,2BAAQ,KAAA,EAAA,EAAI,WAAW,aAAA,EAAe,QAAA,EAAA,cAAU,CAAA;AAAA,EACjD;AAEA,EAAA,uBACC,GAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAW,sCACf,QAAA,kBAAA,GAAA,CAAC,SAAA,EAAA,EAAU,OAAA,EAAkB,IAAA,EAAM,KAAA,EAAO,GAC3C,CAAA;AAEF;ACdA,MAAAE,cAAAA,GAAA,SAMSC,cAAAA,GAAiB;AACzB,EAAA,MAAM,CAACC,QAAAA,EAAUC,WAAW,CAAA,GAAIC,SAAS,KAAK,CAAA;AAE9CC,EAAAA,SAAAA,CAAU,MAAM;AACfF,IAAAA,WAAAA,CAAY,IAAI,CAAA;AAAA,EACjB,CAAA,EAAG,EAAE,CAAA;AAEL,EAAA,IAAID,QAAAA,EAAU;AACb,IAAA,uBACC,IAAA,CAAC,KAAA,EAAA,EAAI,SAAA,EAAU,2BACd,QAAA,EAAA;AAAA,sBAAA,GAAA,CAAC,MAAA,EAAA,EAAO,SAAA,EAAW,eAClB,QAAA,kBAAA,GAAA,CAAC,IAAA,EAAA,EAAK,IAAG,iBAAA,EAAkB,QAAA,EAAA,KAAA,EAAG,GAC/B,CAAA;AAAA,sBAAA,GAAA,CACC,SAAA,EAAA,EAAS,CAAA;AAAA,sBAAA,GAAA,CAET,MAAA,EAAA,EAAM;AAAA,OACR,CAAA;AAAA,EAEF;AACD;;;;"}