import { eq } from 'drizzle-orm';
import { v4 } from 'uuid';
import * as v$1 from 'valibot';
import { d as createServerRpc, b as createServerFn, j as addJokeSchema, e as db, w as joke, x as jokeSchema } from './ssr.mjs';
import 'react/jsx-runtime';
import '@tanstack/react-router';
import '@tanstack/react-query';
import 'react';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'sonner';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import '@tanstack/react-router/ssr/server';

const getJokes_createServerFn_handler = createServerRpc("src_serverFn_jokesServerFn_ts--getJokes_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getJokes.__executeServer(opts, signal);
});
const getJokeById_createServerFn_handler = createServerRpc("src_serverFn_jokesServerFn_ts--getJokeById_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getJokeById.__executeServer(opts, signal);
});
const addJoke_createServerFn_handler = createServerRpc("src_serverFn_jokesServerFn_ts--addJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return addJoke.__executeServer(opts, signal);
});
const updateJoke_createServerFn_handler = createServerRpc("src_serverFn_jokesServerFn_ts--updateJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return updateJoke.__executeServer(opts, signal);
});
const deleteJoke_createServerFn_handler = createServerRpc("src_serverFn_jokesServerFn_ts--deleteJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return deleteJoke.__executeServer(opts, signal);
});
const getJokes = createServerFn({
  method: "GET"
}).handler(getJokes_createServerFn_handler, async () => {
  try {
    return await db.select().from(joke);
  } catch (error) {
    console.error("Failed to read jokes:", error);
    return [];
  }
});
const getJokeById = createServerFn({
  method: "GET"
}).validator(v$1.string()).handler(getJokeById_createServerFn_handler, async ({
  data
}) => {
  try {
    const result = await db.select().from(joke).where(eq(joke.id, data));
    if (result.length === 0) {
      return null;
    }
    return result[0];
  } catch (error) {
    console.error("Failed to read joke:", error);
    return null;
  }
});
const addJoke = createServerFn({
  method: "POST"
}).validator(addJokeSchema).handler(addJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    const newJoke = {
      ...data,
      id: v4()
    };
    const resultId = await db.insert(joke).values(newJoke).returning({
      id: joke.id
    });
    return resultId[0].id;
  } catch (error) {
    console.error("Failed to add joke:", error);
    return "";
  }
});
const updateJoke = createServerFn({
  method: "POST"
}).validator(jokeSchema).handler(updateJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    const existedJoke = await db.select().from(joke).where(eq(joke.id, data.id));
    if (!existedJoke) {
      console.error("Joke does not exist.");
      throw new Error(`Joke does not exist.`);
    }
    const updatedJoke = {
      ...existedJoke,
      question: data.question,
      answer: data.answer
    };
    const result = await db.update(joke).set(updatedJoke).where(eq(joke.id, data.id)).returning({
      id: joke.id
    });
    return result[0].id;
  } catch (error) {
    console.error("Failed to update joke:", error);
    return "";
  }
});
const deleteJoke = createServerFn({
  method: "POST"
}).validator(jokeSchema).handler(deleteJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    await db.delete(joke).where(eq(joke.id, data.id));
    return true;
  } catch (error) {
    console.error("Failed to delete joke:", error);
    return false;
  }
});

export { addJoke_createServerFn_handler, deleteJoke_createServerFn_handler, getJokeById_createServerFn_handler, getJokes_createServerFn_handler, updateJoke_createServerFn_handler };
//# sourceMappingURL=jokesServerFn-DRGMOanE.mjs.map
