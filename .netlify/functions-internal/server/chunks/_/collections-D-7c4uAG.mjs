import { queryCollectionOptions } from '@tanstack/query-db-collection';
import { createCollection } from '@tanstack/react-db';
import { q as queryClient, b as createServerFn, r as fetchUserId, m as deleteJoke, n as updateJoke, o as addJoke, p as getJokes, l as likeJokeSchema, d as createServerRpc, e as db, k as liked } from './ssr.mjs';
import { and, eq } from 'drizzle-orm';
import { v4 } from 'uuid';
import * as v$1 from 'valibot';

const getLikedJokesByUser_createServerFn_handler = createServerRpc("src_serverFn_likesServerFn_ts--getLikedJokesByUser_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getLikedJokesByUser.__executeServer(opts, signal);
});
const getLikedJokesByUser = createServerFn({
  method: "GET"
}).validator(v$1.string()).handler(getLikedJokesByUser_createServerFn_handler, async ({
  data
}) => {
  try {
    return await db.select().from(liked).where(eq(liked.userId, data));
  } catch (error) {
    console.error("Failed to get likes count:", error);
    return [];
  }
});
const createLikedJoke_createServerFn_handler = createServerRpc("src_serverFn_likesServerFn_ts--createLikedJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return createLikedJoke.__executeServer(opts, signal);
});
const createLikedJoke = createServerFn({
  method: "POST"
}).validator(likeJokeSchema).handler(createLikedJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    const existing = await db.select().from(liked).where(and(eq(liked.jokeId, data.jokeId), eq(liked.userId, data.userId)));
    if (existing.length > 0) {
      console.error("Joke already existed.");
      return {
        success: false,
        message: "Failed to like joke"
      };
    }
    await db.insert(liked).values({
      id: v4(),
      jokeId: data.jokeId,
      userId: data.userId
    });
    return {
      success: true,
      message: "Joke liked successfully"
    };
  } catch (error) {
    console.error("Failed to like joke:", error);
    return {
      success: false,
      message: "Failed to like joke"
    };
  }
});
const unlikeJoke_createServerFn_handler = createServerRpc("src_serverFn_likesServerFn_ts--unlikeJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return unlikeJoke.__executeServer(opts, signal);
});
const unlikeJoke = createServerFn({
  method: "POST"
}).validator(likeJokeSchema).handler(unlikeJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    await db.delete(liked).where(and(eq(liked.jokeId, data.jokeId), eq(liked.userId, data.userId)));
    return {
      success: true,
      message: "Joke unliked successfully"
    };
  } catch (error) {
    console.error("Failed to unlike joke:", error);
    return {
      success: false,
      message: "Failed to unlike joke"
    };
  }
});
const likedJokesCollection = createCollection(
  queryCollectionOptions({
    queryClient,
    queryKey: ["likedJokes"],
    queryFn: async () => {
      const { userId } = await fetchUserId();
      if (!userId) {
        return [];
      }
      const likedJokes = await getLikedJokesByUser({
        data: userId
      });
      return likedJokes;
    },
    getKey: (item) => item.id,
    onInsert: async ({ transaction }) => {
      const { modified: newLikedJoke } = transaction.mutations[0];
      await createLikedJoke({ data: newLikedJoke });
    },
    onDelete: async ({ transaction }) => {
      const { original: deletedLikedJoke } = transaction.mutations[0];
      await unlikeJoke({ data: deletedLikedJoke });
    }
  })
);
const jokeCollection = createCollection(
  queryCollectionOptions({
    queryClient,
    queryKey: ["Jokes"],
    queryFn: async () => {
      const jokes = await getJokes();
      return jokes || [];
    },
    getKey: (item) => item.id,
    onInsert: async ({ transaction }) => {
      const { modified: newJoke } = transaction.mutations[0];
      await addJoke({ data: newJoke });
    },
    onUpdate: async ({ transaction }) => {
      const { modified: updatedJoke } = transaction.mutations[0];
      await updateJoke({ data: updatedJoke });
    },
    onDelete: async ({ transaction }) => {
      const { original: deletingJoke } = transaction.mutations[0];
      await deleteJoke({ data: deletingJoke });
    }
  })
);

export { jokeCollection as j, likedJokesCollection as l };
//# sourceMappingURL=collections-D-7c4uAG.mjs.map
