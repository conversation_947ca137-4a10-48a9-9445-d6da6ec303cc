import { jsx, jsxs } from 'react/jsx-runtime';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router';
import { User, Mail } from 'lucide-react';
import { u as useAppForm } from './hook-BE_X5b1f.mjs';
import { toast } from 'sonner';
import { R as Route$4, f as userLoginSchema, g as userRegisterSchema, b as createServerFn, a as authClient, d as createServerRpc, e as db, u as user } from './ssr.mjs';
import { eq } from 'drizzle-orm';
import * as v$1 from 'valibot';
import { formOptions } from '@tanstack/form-core';
import 'react';
import '@tanstack/react-store';
import './input-Cp6Zj0xY.mjs';
import '@tanstack/react-query';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'uuid';
import '@tanstack/react-router/ssr/server';

async function signInWithEmail({ email, password }) {
  await authClient.signIn.email(
    {
      email,
      password,
      callbackURL: "/",
      rememberMe: true
    },
    {
      onSuccess: async () => {
        toast.success("Login successfully");
      },
      onError: async (ctx) => {
        if (ctx.error.status === 403) {
          toast.error("Please verified your email.");
        }
        toast.error(ctx.error.message);
      }
    }
  );
}
async function signUpWithEmail({ email, password, name }) {
  const { data, error } = await authClient.signUp.email(
    {
      email,
      password,
      name,
      callbackURL: "/auth/result"
    },
    {
      onRequest: () => {
      },
      onSuccess: () => {
        toast.success("create new account successfully");
      },
      onError: (ctx) => {
        toast.error(ctx.error.message);
      }
    }
  );
  return { data, error };
}
const getUserByEmail_createServerFn_handler = createServerRpc("src_serverFn_userServerFn_ts--getUserByEmail_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getUserByEmail.__executeServer(opts, signal);
});
const getUserByEmail = createServerFn({
  method: "GET"
}).validator(v$1.string()).handler(getUserByEmail_createServerFn_handler, async ({
  data
}) => {
  try {
    const existedUser = await db.select().from(user).where(eq(user.email, data));
    if (existedUser.length === 0) {
      return null;
    }
    return existedUser[0];
  } catch (error) {
    console.error("Failed to read user by email:", error);
    return null;
  }
});
const deleteUser_createServerFn_handler = createServerRpc("src_serverFn_userServerFn_ts--deleteUser_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return deleteUser.__executeServer(opts, signal);
});
const deleteUser = createServerFn({
  method: "POST"
}).validator(v$1.string()).handler(deleteUser_createServerFn_handler, async ({
  data
}) => {
  try {
    await db.delete(user).where(eq(user.email, data));
    return true;
  } catch (error) {
    console.error("Failed to delete user:", error);
    return false;
  }
});
function AuthForm({ type }) {
  const router = useRouter();
  let formOpts = {};
  if (type === "login") {
    formOpts = formOptions({
      defaultValues: {
        email: "",
        password: ""
      },
      validators: {
        onChange: userLoginSchema
      },
      onSubmit: async ({ value }) => {
        await signInWithEmail(value);
      }
    });
  } else if (type === "register") {
    formOpts = formOptions({
      defaultValues: {
        name: "",
        email: "",
        password: "",
        confirmPassword: ""
      },
      validators: {
        onChange: userRegisterSchema
      },
      onSubmit: async ({ value }) => {
        const existingUser = await getUserByEmail({
          data: value.email
        });
        if (existingUser && !existingUser.emailVerified) {
          await deleteUser({ data: value.email });
        }
        await signUpWithEmail(value);
        router.navigate({ to: "/jokes" });
      }
    });
  } else {
    formOpts = formOptions({
      defaultValues: {
        email: "",
        password: ""
      }
    });
  }
  const form = useAppForm(formOpts);
  return /* @__PURE__ */ jsxs("div", { className: "w-full max-w-md mx-auto", children: [
    /* @__PURE__ */ jsxs("div", { className: "text-center mb-6", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-2xl font-bold", children: type === "login" ? "Welcome Back" : "Create an Account" }),
      /* @__PURE__ */ jsx("p", { className: "text-muted-foreground mt-1", children: type === "login" ? "Sign in to access your account" : "Fill in the details to get started" })
    ] }),
    /* @__PURE__ */ jsxs(
      "form",
      {
        onSubmit: (e) => {
          e.preventDefault();
          form.handleSubmit();
        },
        className: "space-y-4 rounded-xl border bg-card p-6 shadow-sm",
        children: [
          type === "register" && /* @__PURE__ */ jsx(form.AppField, { name: "name", children: (field) => /* @__PURE__ */ jsx(
            field.TextField,
            {
              label: "Name",
              placeholder: "Enter your name",
              icon: /* @__PURE__ */ jsx(User, { className: "h-[18px] w-[18px]" })
            }
          ) }),
          /* @__PURE__ */ jsx(form.AppField, { name: "email", defaultValue: "<EMAIL>", children: (field) => /* @__PURE__ */ jsx(
            field.TextField,
            {
              label: "Email",
              placeholder: "Enter your email",
              type: "email",
              icon: /* @__PURE__ */ jsx(Mail, { className: "h-[18px] w-[18px]" })
            }
          ) }),
          /* @__PURE__ */ jsx(form.AppField, { name: "password", defaultValue: "demo-test", children: (field) => /* @__PURE__ */ jsx(
            field.PasswordField,
            {
              label: "Password",
              placeholder: "Enter your password"
            }
          ) }),
          type === "register" && /* @__PURE__ */ jsx(
            form.AppField,
            {
              name: "confirmPassword",
              validators: {
                onChangeListenTo: ["password"],
                onChange: ({ value, fieldApi }) => {
                  if (value !== fieldApi.form.getFieldValue("password")) {
                    return { message: "password do not match" };
                  }
                  return void 0;
                }
              },
              children: (field) => /* @__PURE__ */ jsx(
                field.PasswordField,
                {
                  label: "Confirm Password",
                  placeholder: "Confirm your password"
                }
              )
            }
          ),
          /* @__PURE__ */ jsx("div", { className: "pt-2", children: /* @__PURE__ */ jsx(form.AppForm, { children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-3", children: [
            /* @__PURE__ */ jsx(
              form.SubscribeButton,
              {
                label: type === "login" ? "Sign In" : "Create Account",
                variant: "default"
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "text-center text-sm text-muted-foreground", children: type === "login" ? /* @__PURE__ */ jsxs("p", { children: [
              "Don't have an account?",
              " ",
              /* @__PURE__ */ jsx(
                Link,
                {
                  to: "/auth",
                  search: { type: "register" },
                  className: "cursor-pointer font-medium text-primary hover:underline",
                  children: "Sign up"
                }
              )
            ] }) : /* @__PURE__ */ jsxs("p", { children: [
              "Already have an account?",
              " ",
              /* @__PURE__ */ jsx(
                Link,
                {
                  to: "/auth",
                  search: { type: "login" },
                  className: "cursor-pointer font-medium text-primary hover:underline",
                  children: "Sign in"
                }
              )
            ] }) })
          ] }) }) })
        ]
      }
    )
  ] });
}
const SplitComponent = function RouteComponent() {
  const {
    type
  } = Route$4.useSearch();
  return /* @__PURE__ */ jsx(AuthForm, { type });
};

export { SplitComponent as component };
//# sourceMappingURL=index-Coe1iut4.mjs.map
