const tsrStartManifest = () => ({ "routes": { "__root__": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/__root.tsx", "children": ["/", "/joke-table", "/jokes", "/auth/result", "/auth/"], "preloads": ["/assets/main-TZzgc6XQ.js"], "assets": [] }, "/": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/index.tsx", "assets": [], "preloads": ["/assets/index-CfcNmPdi.js"] }, "/joke-table": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/joke-table/route.tsx", "children": ["/joke-table/new", "/joke-table/update", "/joke-table/"], "assets": [], "preloads": ["/assets/route-Bkle3yoR.js", "/assets/useLiveQuery-CtZSFPNo.js", "/assets/collections-BNLlw81V.js", "/assets/createLucideIcon-CzbSFJPi.js", "/assets/input-CsTGZlSL.js"] }, "/jokes": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/jokes/route.tsx", "children": ["/jokes/$id", "/jokes/"], "assets": [], "preloads": ["/assets/route-BrpUnMV_.js", "/assets/useLiveQuery-CtZSFPNo.js", "/assets/collections-BNLlw81V.js"] }, "/auth/result": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/auth/result.tsx", "assets": [], "preloads": ["/assets/result-BuDLlHGC.js"] }, "/joke-table/new": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/joke-table/new.tsx", "parent": "/joke-table", "assets": [], "preloads": ["/assets/new-DrOZJ-jh.js", "/assets/JokeForm-BDtJ_9hr.js", "/assets/hook-MFm3XABZ.js"] }, "/joke-table/update": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/joke-table/update.tsx", "parent": "/joke-table", "assets": [], "preloads": ["/assets/update-aW3ygo11.js", "/assets/JokeForm-BDtJ_9hr.js", "/assets/hook-MFm3XABZ.js"] }, "/jokes/$id": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/jokes/$id.tsx", "parent": "/jokes", "assets": [], "preloads": ["/assets/_id-D-mdoKuR.js", "/assets/createLucideIcon-CzbSFJPi.js"] }, "/auth/": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/auth/index.tsx", "assets": [], "preloads": ["/assets/index-qJ0WclPY.js", "/assets/hook-MFm3XABZ.js", "/assets/createLucideIcon-CzbSFJPi.js", "/assets/input-CsTGZlSL.js"] }, "/joke-table/": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/joke-table/index.tsx", "parent": "/joke-table", "assets": [], "preloads": ["/assets/index-Ddbr2MAh.js"] }, "/jokes/": { "filePath": "F:/project/tanstack/tanstack-db-demo/src/routes/jokes/index.tsx", "parent": "/jokes", "assets": [], "preloads": ["/assets/index-5ApTN4Am.js"] } }, "clientEntry": "/assets/main-TZzgc6XQ.js" });

export { tsrStartManifest };
//# sourceMappingURL=_tanstack-start-manifest_v-DCAzDeRf.mjs.map
