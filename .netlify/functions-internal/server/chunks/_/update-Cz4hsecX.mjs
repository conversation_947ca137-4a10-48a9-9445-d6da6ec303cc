import { jsx } from 'react/jsx-runtime';
import { J as JokeForm } from './JokeForm-DjXRjVJ3.mjs';
import { i as Route$2 } from './ssr.mjs';
import 'react';
import 'sonner';
import './hook-BE_X5b1f.mjs';
import '@tanstack/form-core';
import '@tanstack/react-store';
import 'lucide-react';
import './input-Cp6Zj0xY.mjs';
import './collections-D-7c4uAG.mjs';
import '@tanstack/query-db-collection';
import '@tanstack/react-db';
import 'drizzle-orm';
import 'uuid';
import 'valibot';
import '@tanstack/react-router';
import '@tanstack/react-query';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import '@tanstack/react-router/ssr/server';

const SplitComponent = function RouteComponent() {
  const {
    joke
  } = Route$2.useLoaderData();
  return /* @__PURE__ */ jsx(JokeForm, { joke: joke || void 0 });
};

export { SplitComponent as component };
//# sourceMappingURL=update-Cz4hsecX.mjs.map
