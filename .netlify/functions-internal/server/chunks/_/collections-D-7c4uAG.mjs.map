{"version": 3, "file": "collections-D-7c4uAG.mjs", "sources": ["../../../../../../src/serverFn/likesServerFn.ts", "../../../../../../src/db/collections.ts"], "sourcesContent": null, "names": ["getLikedJokesByUser_createServerFn_handler", "createServerRpc", "opts", "signal", "getLikedJokesByUser.__executeServer", "getLikedJokesByUser", "createServerFn", "method", "validator", "v", "string", "handler", "data", "db", "select", "from", "liked", "where", "eq", "userId", "error", "console", "createLikedJoke_createServerFn_handler", "createLikedJoke.__executeServer", "createLikedJoke", "likeJokeSchema", "existing", "and", "jokeId", "length", "success", "message", "insert", "values", "id", "uuidv4", "unlikeJoke_createServerFn_handler", "unlikeJoke.__executeServer", "unlikeJoke", "delete"], "mappings": ";;;;;;;AAOuD,MAAAA,6CAAAC,eAAAA,CAAA,2EAAA,EAAA,YAAA,EAM9C,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAC,mBAAAA,CAAAA,eAAAA,CAAAF,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA,CAAA;AAJF,MAAME,sBAAsBC,cAAAA,CAAe;AAAA,EACjDC,MAAAA,EAAQ;AACT,CAAC,CAAA,CACCC,UAAUC,GAAAA,CAAEC,MAAAA,EAAQ,CAAA,CACpBC,OAAAA,CAAOX,4CAAC,OAAO;AAAA,EAAEY;AAAuB,CAAA,KAAM;AAC9C,EAAA,IAAI;AACH,IAAA,OAAO,MAAMC,EAAAA,CAAGC,MAAAA,EAAAA,CAASC,IAAAA,CAAKC,KAAK,CAAA,CAAEC,KAAAA,CAAMC,EAAAA,CAAGF,KAAAA,CAAMG,MAAAA,EAAQP,IAAI,CAAC,CAAA;AAAA,EAClE,SAASQ,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,8BAA8BA,KAAK,CAAA;AACjD,IAAA,OAAO,EAAA;AAAA,EACR;AACD,CAAC,CAAA;AAAC,MAAAE,yCAAArB,eAAAA,CAAA,uEAAA,EAAA,YAAA,EAMM,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAoB,eAAAA,CAAAA,eAAAA,CAAArB,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA,CAAA;AAJF,MAAMqB,kBAAkBlB,cAAAA,CAAe;AAAA,EAC7CC,MAAAA,EAAQ;AACT,CAAC,EACCC,SAAAA,CAAUiB,cAAc,CAAA,CACxBd,OAAAA,CAAOW,wCAAC,OAAO;AAAA,EAAEV;AAA8B,CAAA,KAAM;AACrD,EAAA,IAAI;AAEH,IAAA,MAAMc,QAAAA,GAAW,MAAMb,EAAAA,CACrBC,MAAAA,GACAC,IAAAA,CAAKC,KAAK,CAAA,CACVC,KAAAA,CACAU,GAAAA,CAAIT,EAAAA,CAAGF,MAAMY,MAAAA,EAAQhB,IAAAA,CAAKgB,MAAM,CAAA,EAAGV,EAAAA,CAAGF,MAAMG,MAAAA,EAAQP,IAAAA,CAAKO,MAAM,CAAC,CACjE,CAAA;AAED,IAAA,IAAIO,QAAAA,CAASG,SAAS,CAAA,EAAG;AACxBR,MAAAA,OAAAA,CAAQD,MAAM,uBAAuB,CAAA;AACrC,MAAA,OAAO;AAAA,QAAEU,OAAAA,EAAS,KAAA;AAAA,QAAOC,OAAAA,EAAS;AAAA,OAAA;AAAA,IACnC;AAGA,IAAA,MAAMlB,EAAAA,CAAGmB,MAAAA,CAAOhB,KAAK,CAAA,CAAEiB,MAAAA,CAAO;AAAA,MAC7BC,IAAIC,EAAAA,EAAAA;AAAAA,MACJP,QAAQhB,IAAAA,CAAKgB,MAAAA;AAAAA,MACbT,QAAQP,IAAAA,CAAKO;AAAAA,KACb,CAAA;AAED,IAAA,OAAO;AAAA,MAAEW,OAAAA,EAAS,IAAA;AAAA,MAAMC,OAAAA,EAAS;AAAA,KAAA;AAAA,EAClC,SAASX,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,wBAAwBA,KAAK,CAAA;AAC3C,IAAA,OAAO;AAAA,MAAEU,OAAAA,EAAS,KAAA;AAAA,MAAOC,OAAAA,EAAS;AAAA,KAAA;AAAA,EACnC;AACD,CAAC,CAAA;AAEF,MAAAK,oCAAAnC,eAAAA,CAAA,kEAAA,EAAA,YAAA,EAKS,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAkC,UAAAA,CAAAA,eAAAA,CAAAnC,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA,CAAA;AAJF,MAAMmC,aAAahC,cAAAA,CAAe;AAAA,EACxCC,MAAAA,EAAQ;AACT,CAAC,EACCC,SAAAA,CAAUiB,cAAc,CAAA,CACxBd,OAAAA,CAAOyB,mCAAC,OAAO;AAAA,EAAExB;AAA8B,CAAA,KAAM;AACrD,EAAA,IAAI;AACH,IAAA,MAAMC,GACJ0B,MAAAA,CAAOvB,KAAK,EACZC,KAAAA,CACAU,GAAAA,CAAIT,GAAGF,KAAAA,CAAMY,MAAAA,EAAQhB,IAAAA,CAAKgB,MAAM,GAAGV,EAAAA,CAAGF,KAAAA,CAAMG,QAAQP,IAAAA,CAAKO,MAAM,CAAC,CACjE,CAAA;AAED,IAAA,OAAO;AAAA,MAAEW,OAAAA,EAAS,IAAA;AAAA,MAAMC,OAAAA,EAAS;AAAA,KAAA;AAAA,EAClC,SAASX,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,0BAA0BA,KAAK,CAAA;AAC7C,IAAA,OAAO;AAAA,MAAEU,OAAAA,EAAS,KAAA;AAAA,MAAOC,OAAAA,EAAS;AAAA,KAAA;AAAA,EACnC;AACD,CAAC,CAAA;ACxDK,MAAM,oBAAA,GAAuB,gBAAA;AAAA,EACnC,sBAAA,CAAuB;AAAA,IACtB,WAAA;AAAA,IACA,QAAA,EAAU,CAAC,YAAY,CAAA;AAAA,IACvB,SAAS,YAAY;AACpB,MAAA,MAAM,EAAE,MAAA,EAAA,GAAW,MAAM,WAAA,EAAA;AAEzB,MAAA,IAAI,CAAC,MAAA,EAAQ;AACZ,QAAA,OAAO,EAAA;AAAA,MACR;AAEA,MAAA,MAAM,UAAA,GAAgC,MAAM,mBAAA,CAAoB;AAAA,QAC/D,IAAA,EAAM;AAAA,OACN,CAAA;AAED,MAAA,OAAO,UAAA;AAAA,IACR,CAAA;AAAA,IACA,MAAA,EAAQ,CAAC,IAAA,KAAS,IAAA,CAAK,EAAA;AAAA,IACvB,QAAA,EAAU,OAAO,EAAE,WAAA,EAAA,KAAkB;AACpC,MAAA,MAAM,EAAE,QAAA,EAAU,YAAA,EAAA,GAAiB,WAAA,CAAY,UAAU,CAAC,CAAA;AAC1D,MAAA,MAAM,eAAA,CAAgB,EAAE,IAAA,EAAM,YAAA,EAAc,CAAA;AAAA,IAC7C,CAAA;AAAA,IAEA,QAAA,EAAU,OAAO,EAAE,WAAA,EAAA,KAAkB;AACpC,MAAA,MAAM,EAAE,QAAA,EAAU,gBAAA,EAAA,GAAqB,WAAA,CAAY,UAAU,CAAC,CAAA;AAC9D,MAAA,MAAM,UAAA,CAAW,EAAE,IAAA,EAAM,gBAAA,EAAkB,CAAA;AAAA,IAC5C;AAAA,GACA;AACF;AAEO,MAAM,cAAA,GAAiB,gBAAA;AAAA,EAC7B,sBAAA,CAAuB;AAAA,IACtB,WAAA;AAAA,IACA,QAAA,EAAU,CAAC,OAAO,CAAA;AAAA,IAClB,SAAS,YAAY;AACpB,MAAA,MAAM,KAAA,GAAsB,MAAM,QAAA,EAAA;AAElC,MAAA,OAAO,SAAS,EAAA;AAAA,IACjB,CAAA;AAAA,IACA,MAAA,EAAQ,CAAC,IAAA,KAAS,IAAA,CAAK,EAAA;AAAA,IAEvB,QAAA,EAAU,OAAO,EAAE,WAAA,EAAA,KAAkB;AACpC,MAAA,MAAM,EAAE,QAAA,EAAU,OAAA,EAAA,GAAY,WAAA,CAAY,UAAU,CAAC,CAAA;AACrD,MAAA,MAAM,OAAA,CAAQ,EAAE,IAAA,EAAM,OAAA,EAAS,CAAA;AAAA,IAChC,CAAA;AAAA,IAEA,QAAA,EAAU,OAAO,EAAE,WAAA,EAAA,KAAkB;AACpC,MAAA,MAAM,EAAE,QAAA,EAAU,WAAA,EAAA,GAAgB,WAAA,CAAY,UAAU,CAAC,CAAA;AACzD,MAAA,MAAM,UAAA,CAAW,EAAE,IAAA,EAAM,WAAA,EAAa,CAAA;AAAA,IACvC,CAAA;AAAA,IAEA,QAAA,EAAU,OAAO,EAAE,WAAA,EAAA,KAAkB;AACpC,MAAA,MAAM,EAAE,QAAA,EAAU,YAAA,EAAA,GAAiB,WAAA,CAAY,UAAU,CAAC,CAAA;AAC1D,MAAA,MAAM,UAAA,CAAW,EAAE,IAAA,EAAM,YAAA,EAAc,CAAA;AAAA,IACxC;AAAA,GACA;AACF;;;;"}