import { jsx, Fragment, jsxs } from 'react/jsx-runtime';
import { useMemo, useState, useEffect, createContext, useContext } from 'react';
import { FieldGroupApi, FormApi, functionalUpdate, FieldApi } from '@tanstack/form-core';
import { useStore } from '@tanstack/react-store';
import { c as cn, B as Button } from './ssr.mjs';
import { Lock, EyeOff, Eye } from 'lucide-react';
import { I as Input } from './input-Cp6Zj0xY.mjs';

const useIsomorphicLayoutEffect = useEffect;
function useField(opts) {
  const [fieldApi] = useState(() => {
    const api = new FieldApi({
      ...opts,
      form: opts.form,
      name: opts.name
    });
    const extendedApi = api;
    extendedApi.Field = Field;
    return extendedApi;
  });
  useIsomorphicLayoutEffect(fieldApi.mount, [fieldApi]);
  useIsomorphicLayoutEffect(() => {
    fieldApi.update(opts);
  });
  useStore(
    fieldApi.store,
    opts.mode === "array" ? (state) => {
      var _a;
      return [
        state.meta,
        Object.keys((_a = state.value) != null ? _a : []).length
      ];
    } : void 0
  );
  return fieldApi;
}
const Field = ({
  children,
  ...fieldOptions
}) => {
  const fieldApi = useField(fieldOptions);
  const jsxToDisplay = useMemo(
    () => functionalUpdate(children, fieldApi),
    /**
     * The reason this exists is to fix an issue with the React Compiler.
     * Namely, functionalUpdate is memoized where it checks for `fieldApi`, which is a static type.
     * This means that when `state.value` changes, it does not trigger a re-render. The useMemo explicitly fixes this problem
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [children, fieldApi, fieldApi.state.value, fieldApi.state.meta]
  );
  return /* @__PURE__ */ jsx(Fragment, { children: jsxToDisplay });
};
function LocalSubscribe$1({
  form,
  selector,
  children
}) {
  const data = useStore(form.store, selector);
  return functionalUpdate(children, data);
}
function useForm(opts) {
  const [formApi] = useState(() => {
    const api = new FormApi(opts);
    const extendedApi = api;
    extendedApi.Field = function APIField(props) {
      return /* @__PURE__ */ jsx(Field, { ...props, form: api });
    };
    extendedApi.Subscribe = function Subscribe(props) {
      return /* @__PURE__ */ jsx(
        LocalSubscribe$1,
        {
          form: api,
          selector: props.selector,
          children: props.children
        }
      );
    };
    return extendedApi;
  });
  useIsomorphicLayoutEffect(formApi.mount, []);
  useIsomorphicLayoutEffect(() => {
    formApi.update(opts);
  });
  return formApi;
}
function LocalSubscribe({
  lens,
  selector,
  children
}) {
  const data = useStore(lens.store, selector);
  return functionalUpdate(children, data);
}
function useFieldGroup(opts) {
  const [formLensApi] = useState(() => {
    const api = new FieldGroupApi(opts);
    const form = opts.form instanceof FieldGroupApi ? opts.form.form : opts.form;
    const extendedApi = api;
    extendedApi.AppForm = function AppForm(appFormProps) {
      return /* @__PURE__ */ jsx(form.AppForm, { ...appFormProps });
    };
    extendedApi.AppField = function AppField({ name, ...appFieldProps }) {
      return /* @__PURE__ */ jsx(
        form.AppField,
        {
          name: formLensApi.getFormFieldName(name),
          ...appFieldProps
        }
      );
    };
    extendedApi.Field = function Field2({ name, ...fieldProps }) {
      return /* @__PURE__ */ jsx(
        form.Field,
        {
          name: formLensApi.getFormFieldName(name),
          ...fieldProps
        }
      );
    };
    extendedApi.Subscribe = function Subscribe(props) {
      return /* @__PURE__ */ jsx(
        LocalSubscribe,
        {
          lens: formLensApi,
          selector: props.selector,
          children: props.children
        }
      );
    };
    return Object.assign(extendedApi, {
      ...opts.formComponents
    });
  });
  useIsomorphicLayoutEffect(formLensApi.mount, [formLensApi]);
  return formLensApi;
}
const fieldContext$1 = createContext(null);
const formContext$1 = createContext(null);
function createFormHookContexts() {
  function useFieldContext2() {
    const field = useContext(fieldContext$1);
    if (!field) {
      throw new Error(
        "`fieldContext` only works when within a `fieldComponent` passed to `createFormHook`"
      );
    }
    return field;
  }
  function useFormContext2() {
    const form = useContext(formContext$1);
    if (!form) {
      throw new Error(
        "`formContext` only works when within a `formComponent` passed to `createFormHook`"
      );
    }
    return form;
  }
  return { fieldContext: fieldContext$1, useFieldContext: useFieldContext2, useFormContext: useFormContext2, formContext: formContext$1 };
}
function createFormHook({
  fieldComponents,
  fieldContext: fieldContext2,
  formContext: formContext2,
  formComponents
}) {
  function useAppForm2(props) {
    const form = useForm(props);
    const AppForm = useMemo(() => {
      const AppForm2 = ({ children }) => {
        return /* @__PURE__ */ jsx(formContext2.Provider, { value: form, children });
      };
      return AppForm2;
    }, [form]);
    const AppField = useMemo(() => {
      const AppField2 = ({ children, ...props2 }) => {
        return /* @__PURE__ */ jsx(form.Field, { ...props2, children: (field) => (
          // eslint-disable-next-line @eslint-react/no-context-provider
          /* @__PURE__ */ jsx(fieldContext2.Provider, { value: field, children: children(Object.assign(field, fieldComponents)) })
        ) });
      };
      return AppField2;
    }, [form]);
    const extendedForm = useMemo(() => {
      return Object.assign(form, {
        AppField,
        AppForm,
        ...formComponents
      });
    }, [form, AppField, AppForm]);
    return extendedForm;
  }
  function withForm({
    render,
    props
  }) {
    return (innerProps) => render({ ...props, ...innerProps });
  }
  function withFieldGroup({
    render,
    props,
    defaultValues
  }) {
    return function Render(innerProps) {
      const fieldGroupProps = useMemo(() => {
        return {
          form: innerProps.form,
          fields: innerProps.fields,
          defaultValues,
          formComponents
        };
      }, [innerProps.form, innerProps.fields]);
      const fieldGroupApi = useFieldGroup(fieldGroupProps);
      return render({ ...props, ...innerProps, group: fieldGroupApi });
    };
  }
  return {
    useAppForm: useAppForm2,
    withForm,
    withFieldGroup
  };
}
const { fieldContext, formContext, useFieldContext, useFormContext } = createFormHookContexts();
function LoadingSpinner() {
  return (
    // biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
    /* @__PURE__ */ jsxs(
      "svg",
      {
        className: "animate-spin -ml-1 size-4",
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        children: [
          /* @__PURE__ */ jsx(
            "circle",
            {
              className: "opacity-25",
              cx: "12",
              cy: "12",
              r: "10",
              stroke: "currentColor",
              strokeWidth: "4"
            }
          ),
          /* @__PURE__ */ jsx(
            "path",
            {
              className: "opacity-75",
              fill: "currentColor",
              d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            }
          )
        ]
      }
    )
  );
}
function SubscribeButton({
  label,
  variant = "default"
}) {
  const form = useFormContext();
  return /* @__PURE__ */ jsx(form.Subscribe, { selector: (state) => state.isSubmitting, children: (isSubmitting) => /* @__PURE__ */ jsx(
    Button,
    {
      variant,
      type: "submit",
      disabled: isSubmitting,
      className: "min-w-[100px]",
      children: isSubmitting ? /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsx(LoadingSpinner, {}),
        /* @__PURE__ */ jsx("span", { children: "Submitting" })
      ] }) : label
    }
  ) });
}
function FieldInfo({ field }) {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    field.state.meta.isTouched && !field.state.meta.isValid ? /* @__PURE__ */ jsx("p", { className: "text-sm text-destructive mt-1", children: field.state.meta.errors.map((e) => e.message).join(", ") }) : null,
    field.state.meta.isValidating ? /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground mt-1", children: "Validating..." }) : null
  ] });
}
function TextField({
  label,
  placeholder,
  type,
  icon
}) {
  const field = useFieldContext();
  return /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
    /* @__PURE__ */ jsxs("label", { className: "flex flex-col gap-1.5", children: [
      /* @__PURE__ */ jsx("div", { className: "text-sm font-medium", children: label }),
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        icon && /* @__PURE__ */ jsx("div", { className: "absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground", children: icon }),
        /* @__PURE__ */ jsx(
          Input,
          {
            type,
            value: field.state.value || "",
            onChange: (e) => field.handleChange(e.target.value),
            onBlur: field.handleBlur,
            placeholder,
            className: cn(icon && "pl-10")
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsx(FieldInfo, { field })
  ] });
}
function PasswordField({
  label,
  placeholder
}) {
  const field = useFieldContext();
  const [showPassword, setShowPassword] = useState(false);
  return /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
    /* @__PURE__ */ jsxs("label", { className: "flex flex-col gap-1.5", children: [
      /* @__PURE__ */ jsx("div", { className: "text-sm font-medium", children: label }),
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx("div", { className: "absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground", children: /* @__PURE__ */ jsx(Lock, { className: "h-[18px] w-[18px]" }) }),
        /* @__PURE__ */ jsx(
          Input,
          {
            type: showPassword ? "text" : "password",
            value: field.state.value || "",
            onChange: (e) => field.handleChange(e.target.value),
            onBlur: field.handleBlur,
            placeholder,
            className: "pl-10 pr-10"
          }
        ),
        /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            className: "cursor-pointer absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors",
            onClick: () => setShowPassword(!showPassword),
            children: showPassword ? /* @__PURE__ */ jsx(EyeOff, { size: 18 }) : /* @__PURE__ */ jsx(Eye, { size: 18 })
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsx(FieldInfo, { field })
  ] });
}
const { useAppForm } = createFormHook({
  fieldComponents: {
    TextField,
    PasswordField
  },
  formComponents: {
    SubscribeButton
  },
  fieldContext,
  formContext
});

export { useAppForm as u };
//# sourceMappingURL=hook-BE_X5b1f.mjs.map
