const _tanstackStartServerFnManifest_v = {
  "src_lib_auth_fetchUserId_ts--fetchUserId_createServerFn_handler": {
    functionName: "fetchUserId_createServerFn_handler",
    importer: () => import('./fetchUserId-D1MJg7WG.mjs')
  },
  "src_serverFn_jokesServerFn_ts--getJokes_createServerFn_handler": {
    functionName: "getJokes_createServerFn_handler",
    importer: () => import('./jokesServerFn-DRGMOanE.mjs')
  },
  "src_serverFn_jokesServerFn_ts--getJokeById_createServerFn_handler": {
    functionName: "getJokeById_createServerFn_handler",
    importer: () => import('./jokesServerFn-DRGMOanE.mjs')
  },
  "src_serverFn_jokesServerFn_ts--addJoke_createServerFn_handler": {
    functionName: "addJoke_createServerFn_handler",
    importer: () => import('./jokesServerFn-DRGMOanE.mjs')
  },
  "src_serverFn_jokesServerFn_ts--updateJoke_createServerFn_handler": {
    functionName: "updateJoke_createServerFn_handler",
    importer: () => import('./jokesServerFn-DRGMOanE.mjs')
  },
  "src_serverFn_jokesServerFn_ts--deleteJoke_createServerFn_handler": {
    functionName: "deleteJoke_createServerFn_handler",
    importer: () => import('./jokesServerFn-DRGMOanE.mjs')
  },
  "src_serverFn_userServerFn_ts--getUserByEmail_createServerFn_handler": {
    functionName: "getUserByEmail_createServerFn_handler",
    importer: () => import('./userServerFn-4JaURXhu.mjs')
  },
  "src_serverFn_userServerFn_ts--deleteUser_createServerFn_handler": {
    functionName: "deleteUser_createServerFn_handler",
    importer: () => import('./userServerFn-4JaURXhu.mjs')
  },
  "src_serverFn_likesServerFn_ts--getLikedJokesByUser_createServerFn_handler": {
    functionName: "getLikedJokesByUser_createServerFn_handler",
    importer: () => import('./likesServerFn-BPAxbBtG.mjs')
  },
  "src_serverFn_likesServerFn_ts--createLikedJoke_createServerFn_handler": {
    functionName: "createLikedJoke_createServerFn_handler",
    importer: () => import('./likesServerFn-BPAxbBtG.mjs')
  },
  "src_serverFn_likesServerFn_ts--unlikeJoke_createServerFn_handler": {
    functionName: "unlikeJoke_createServerFn_handler",
    importer: () => import('./likesServerFn-BPAxbBtG.mjs')
  }
};

export { _tanstackStartServerFnManifest_v as default };
//# sourceMappingURL=_tanstack-start-server-fn-manifest_v-jV-eCsRg.mjs.map
