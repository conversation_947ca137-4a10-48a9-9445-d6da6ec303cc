{"version": 3, "file": "jokesServerFn-DRGMOanE.mjs", "sources": ["../../../../../../src/serverFn/jokesServerFn.ts?tsr-directive-use-server="], "sourcesContent": null, "names": ["getJokes_createServerFn_handler", "createServerRpc", "opts", "signal", "getJokes.__executeServer", "getJokeById_createServerFn_handler", "getJokeById.__executeServer", "addJoke_createServerFn_handler", "addJoke.__executeServer", "updateJoke_createServerFn_handler", "updateJoke.__executeServer", "deleteJoke_createServerFn_handler", "deleteJoke.__executeServer", "getJokes", "createServerFn", "method", "handler", "db", "select", "from", "joke", "error", "console", "getJokeById", "validator", "v", "string", "data", "result", "where", "eq", "id", "length", "addJoke", "addJokeSchema", "newJoke", "uuidv4", "resultId", "insert", "values", "returning", "updateJoke", "jokeSchema", "existedJoke", "Error", "updatedJoke", "question", "answer", "update", "set", "deleteJoke", "delete"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAeA,MAAAA,kCAAAC,eAAAA,CAAA,gEAAA,EAAA,YAAA,EAMU,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAC,QAAAA,CAAAA,eAAAA,CAAAF,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAAA,MAAAE,qCAAAJ,eAAAA,CAAA,mEAAA,EAAA,YAAA,EAaD,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAG,WAAAA,CAAAA,eAAAA,CAAAJ,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAAA,MAAAI,iCAAAN,eAAAA,CAAA,+DAAA,EAAA,YAAA,EAiBA,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAK,OAAAA,CAAAA,eAAAA,CAAAN,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAAA,MAAAM,oCAAAR,eAAAA,CAAA,kEAAA,EAAA,YAAA,EAsBA,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAO,UAAAA,CAAAA,eAAAA,CAAAR,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAAA,MAAAQ,oCAAAV,eAAAA,CAAA,kEAAA,EAAA,YAAA,EAmCA,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAS,UAAAA,CAAAA,eAAAA,CAAAV,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAzFF,MAAMU,WAAWC,cAAAA,CAAe;AAAA,EACtCC,MAAAA,EAAQ;AACT,CAAC,CAAA,CAAEC,OAAAA,CAAOhB,+BAAAA,EAAC,YAAY;AACtB,EAAA,IAAI;AACH,IAAA,OAAO,MAAMiB,EAAAA,CAAGC,MAAAA,EAAAA,CAASC,KAAKC,IAAI,CAAA;AAAA,EACnC,SAASC,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,yBAAyBA,KAAK,CAAA;AAC5C,IAAA,OAAO,EAAA;AAAA,EACR;AACD,CAAC,CAAA;AAEM,MAAME,cAAcT,cAAAA,CAAe;AAAA,EACzCC,MAAAA,EAAQ;AACT,CAAC,CAAA,CACCS,UAAUC,GAAAA,CAAEC,MAAAA,EAAQ,CAAA,CACpBV,OAAAA,CAAOX,oCAAC,OAAO;AAAA,EAAEsB;AAAK,CAAA,KAAM;AAC5B,EAAA,IAAI;AACH,IAAA,MAAMC,MAAAA,GAAS,MAAMX,EAAAA,CAAGC,MAAAA,EAAAA,CAASC,IAAAA,CAAKC,IAAI,CAAA,CAAES,KAAAA,CAAMC,EAAAA,CAAGV,IAAAA,CAAKW,EAAAA,EAAIJ,IAAI,CAAC,CAAA;AACnE,IAAA,IAAIC,MAAAA,CAAOI,WAAW,CAAA,EAAG;AACxB,MAAA,OAAO,IAAA;AAAA,IACR;AACA,IAAA,OAAOJ,OAAO,CAAC,CAAA;AAAA,EAChB,SAASP,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,wBAAwBA,KAAK,CAAA;AAC3C,IAAA,OAAO,IAAA;AAAA,EACR;AACD,CAAC,CAAA;AAEK,MAAMY,UAAUnB,cAAAA,CAAe;AAAA,EACrCC,MAAAA,EAAQ;AACT,CAAC,EACCS,SAAAA,CAAUU,aAAa,CAAA,CACvBlB,OAAAA,CAAOT,gCAAC,OAAO;AAAA,EAAEoB;AAA0B,CAAA,KAAM;AACjD,EAAA,IAAI;AACH,IAAA,MAAMQ,OAAAA,GAAsB;AAAA,MAC3B,GAAGR,IAAAA;AAAAA,MACHI,IAAIK,EAAAA;AAAAA,KAAO;AAGZ,IAAA,MAAMC,QAAAA,GAAW,MAAMpB,EAAAA,CACrBqB,MAAAA,CAAOlB,IAAI,CAAA,CACXmB,MAAAA,CAAOJ,OAAO,CAAA,CACdK,SAAAA,CAAU;AAAA,MAAET,IAAIX,IAAAA,CAAKW;AAAAA,KAAI,CAAA;AAC3B,IAAA,OAAOM,QAAAA,CAAS,CAAC,CAAA,CAAEN,EAAAA;AAAAA,EACpB,SAASV,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,uBAAuBA,KAAK,CAAA;AAC1C,IAAA,OAAO,EAAA;AAAA,EACR;AACD,CAAC,CAAA;AAEK,MAAMoB,aAAa3B,cAAAA,CAAe;AAAA,EACxCC,MAAAA,EAAQ;AACT,CAAC,EACCS,SAAAA,CAAUkB,UAAU,CAAA,CACpB1B,OAAAA,CAAOP,mCAAC,OAAO;AAAA,EAAEkB;AAA2B,CAAA,KAAM;AAClD,EAAA,IAAI;AACH,IAAA,MAAMgB,WAAAA,GAAc,MAAM1B,EAAAA,CACxBC,MAAAA,GACAC,IAAAA,CAAKC,IAAI,CAAA,CACTS,KAAAA,CAAMC,EAAAA,CAAGV,IAAAA,CAAKW,EAAAA,EAAIJ,IAAAA,CAAKI,EAAE,CAAC,CAAA;AAE5B,IAAA,IAAI,CAACY,WAAAA,EAAa;AACjBrB,MAAAA,OAAAA,CAAQD,MAAM,sBAAsB,CAAA;AACpC,MAAA,MAAM,IAAIuB,MAAM,CAAA,oBAAA,CAAsB,CAAA;AAAA,IACvC;AAEA,IAAA,MAAMC,WAAAA,GAAc;AAAA,MACnB,GAAGF,WAAAA;AAAAA,MACHG,UAAUnB,IAAAA,CAAKmB,QAAAA;AAAAA,MACfC,QAAQpB,IAAAA,CAAKoB;AAAAA,KAAAA;AAGd,IAAA,MAAMnB,SAAS,MAAMX,EAAAA,CACnB+B,MAAAA,CAAO5B,IAAI,EACX6B,GAAAA,CAAIJ,WAAW,CAAA,CACfhB,KAAAA,CAAMC,GAAGV,IAAAA,CAAKW,EAAAA,EAAIJ,KAAKI,EAAE,CAAC,EAC1BS,SAAAA,CAAU;AAAA,MAAET,IAAIX,IAAAA,CAAKW;AAAAA,KAAI,CAAA;AAE3B,IAAA,OAAOH,MAAAA,CAAO,CAAC,CAAA,CAAEG,EAAAA;AAAAA,EAClB,SAASV,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,0BAA0BA,KAAK,CAAA;AAC7C,IAAA,OAAO,EAAA;AAAA,EACR;AACD,CAAC,CAAA;AAEK,MAAM6B,aAAapC,cAAAA,CAAe;AAAA,EACxCC,MAAAA,EAAQ;AACT,CAAC,EACCS,SAAAA,CAAUkB,UAAU,CAAA,CACpB1B,OAAAA,CAAOL,mCAAC,OAAO;AAAA,EAAEgB;AAA2B,CAAA,KAAM;AAClD,EAAA,IAAI;AACH,IAAA,MAAMV,EAAAA,CAAGkC,MAAAA,CAAO/B,IAAI,CAAA,CAAES,KAAAA,CAAMC,GAAGV,IAAAA,CAAKW,EAAAA,EAAIJ,IAAAA,CAAKI,EAAE,CAAC,CAAA;AAChD,IAAA,OAAO,IAAA;AAAA,EACR,SAASV,KAAAA,EAAO;AACfC,IAAAA,OAAAA,CAAQD,KAAAA,CAAM,0BAA0BA,KAAK,CAAA;AAC7C,IAAA,OAAO,KAAA;AAAA,EACR;AACD,CAAC,CAAA;;;;"}