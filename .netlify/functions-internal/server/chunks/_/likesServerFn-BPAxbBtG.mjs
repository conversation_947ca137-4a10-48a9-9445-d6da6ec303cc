import { and, eq } from 'drizzle-orm';
import { v4 } from 'uuid';
import * as v$1 from 'valibot';
import { d as createServerRpc, b as createServerFn, l as likeJokeSchema, e as db, k as liked } from './ssr.mjs';
import 'react/jsx-runtime';
import '@tanstack/react-router';
import '@tanstack/react-query';
import 'react';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'sonner';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import '@tanstack/react-router/ssr/server';

const getLikedJokesByUser_createServerFn_handler = createServerRpc("src_serverFn_likesServerFn_ts--getLikedJokesByUser_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return getLikedJokesByUser.__executeServer(opts, signal);
});
const createLikedJoke_createServerFn_handler = createServerRpc("src_serverFn_likesServerFn_ts--createLikedJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return createLikedJoke.__executeServer(opts, signal);
});
const unlikeJoke_createServerFn_handler = createServerRpc("src_serverFn_likesServerFn_ts--unlikeJoke_createServerFn_handler", "/_serverFn", (opts, signal) => {
  return unlikeJoke.__executeServer(opts, signal);
});
const getLikedJokesByUser = createServerFn({
  method: "GET"
}).validator(v$1.string()).handler(getLikedJokesByUser_createServerFn_handler, async ({
  data
}) => {
  try {
    return await db.select().from(liked).where(eq(liked.userId, data));
  } catch (error) {
    console.error("Failed to get likes count:", error);
    return [];
  }
});
const createLikedJoke = createServerFn({
  method: "POST"
}).validator(likeJokeSchema).handler(createLikedJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    const existing = await db.select().from(liked).where(and(eq(liked.jokeId, data.jokeId), eq(liked.userId, data.userId)));
    if (existing.length > 0) {
      console.error("Joke already existed.");
      return {
        success: false,
        message: "Failed to like joke"
      };
    }
    await db.insert(liked).values({
      id: v4(),
      jokeId: data.jokeId,
      userId: data.userId
    });
    return {
      success: true,
      message: "Joke liked successfully"
    };
  } catch (error) {
    console.error("Failed to like joke:", error);
    return {
      success: false,
      message: "Failed to like joke"
    };
  }
});
const unlikeJoke = createServerFn({
  method: "POST"
}).validator(likeJokeSchema).handler(unlikeJoke_createServerFn_handler, async ({
  data
}) => {
  try {
    await db.delete(liked).where(and(eq(liked.jokeId, data.jokeId), eq(liked.userId, data.userId)));
    return {
      success: true,
      message: "Joke unliked successfully"
    };
  } catch (error) {
    console.error("Failed to unlike joke:", error);
    return {
      success: false,
      message: "Failed to unlike joke"
    };
  }
});

export { createLikedJoke_createServerFn_handler, getLikedJokesByUser_createServerFn_handler, unlikeJoke_createServerFn_handler };
//# sourceMappingURL=likesServerFn-BPAxbBtG.mjs.map
