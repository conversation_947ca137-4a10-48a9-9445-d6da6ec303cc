import { jsx, jsxs } from 'react/jsx-runtime';
import { useState, useEffect } from 'react';
import { useLiveQuery, eq } from '@tanstack/react-db';
import { Heart, HeartOff } from 'lucide-react';
import { toast } from 'sonner';
import { h as Route$3, B as Button } from './ssr.mjs';
import { l as likedJokesCollection } from './collections-D-7c4uAG.mjs';
import '@tanstack/react-router';
import '@tanstack/react-query';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'valibot';
import 'drizzle-orm';
import 'uuid';
import '@tanstack/react-router/ssr/server';
import '@tanstack/query-db-collection';

function JokeDetail({ joke, userId }) {
  const { data: likedJokesByUser } = useLiveQuery(
    (q) => q.from({ likedJoke: likedJokesCollection }).where(({ likedJoke }) => eq(likedJoke.jokeId, joke.id))
  );
  const isLiked = likedJokesByUser.find(
    (likedJoke) => likedJoke.jokeId === joke.id
  );
  const addLikedJoke = () => {
    if (!userId) {
      toast.error("Please login to like a joke.");
      return;
    }
    likedJokesCollection.insert({
      id: "",
      jokeId: joke.id,
      userId,
      createdAt: /* @__PURE__ */ new Date()
    });
  };
  const removeLikedJoke = () => {
    likedJokesCollection.delete((isLiked == null ? void 0 : isLiked.id) || "");
  };
  return /* @__PURE__ */ jsx("div", { className: "max-w-3xl mx-auto p-6", children: /* @__PURE__ */ jsxs("div", { className: "bg-card rounded-xl shadow-lg p-8 border border-border/50 backdrop-blur-sm", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex items-start gap-4 mb-8", children: [
      /* @__PURE__ */ jsx("div", { className: "text-4xl", children: "\u{1F3AD}" }),
      /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
        /* @__PURE__ */ jsx("h1", { className: "text-3xl font-bold text-card-foreground mb-6 leading-tight", children: joke.question }),
        /* @__PURE__ */ jsxs("div", { className: "bg-gradient-to-r from-primary/10 to-secondary/10 p-6 rounded-lg border border-primary/20 relative overflow-hidden", children: [
          /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 text-2xl opacity-20", children: "\u{1F602}" }),
          /* @__PURE__ */ jsx("p", { className: "text-xl text-card-foreground font-medium leading-relaxed", children: joke.answer })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "flex items-center justify-between pt-6 border-t border-border/50", children: /* @__PURE__ */ jsx(
      Button,
      {
        variant: isLiked ? "default" : "outline",
        size: "lg",
        className: `group transition-all duration-200 ${isLiked ? "bg-red-500 hover:bg-red-600 text-white shadow-lg hover:shadow-red-500/25" : "hover:bg-red-50 hover:border-red-200 hover:text-red-600"}`,
        onClick: isLiked ? removeLikedJoke : addLikedJoke,
        children: /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
          isLiked ? /* @__PURE__ */ jsx(Heart, { className: "w-5 h-5 fill-current group-hover:scale-110 transition-transform" }) : /* @__PURE__ */ jsx(HeartOff, { className: "w-5 h-5 group-hover:scale-110 transition-transform" }),
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: isLiked ? "Liked!" : "Like this joke" })
        ] })
      }
    ) })
  ] }) });
}
const SplitComponent = function RouteComponent() {
  const {
    joke,
    userId
  } = Route$3.useLoaderData();
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  if (!joke) {
    return /* @__PURE__ */ jsx("p", { children: "Joke not found!" });
  }
  if (isClient) {
    return /* @__PURE__ */ jsx(JokeDetail, { userId: userId || "", joke });
  }
  return /* @__PURE__ */ jsx("p", { children: "Loading..." });
};

export { SplitComponent as component };
//# sourceMappingURL=_id-BdYWfdBo.mjs.map
