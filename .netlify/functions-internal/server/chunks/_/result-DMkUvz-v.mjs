import { jsx } from 'react/jsx-runtime';
import { s as Route } from './ssr.mjs';
import '@tanstack/react-router';
import '@tanstack/react-query';
import 'react';
import '@radix-ui/react-slot';
import 'class-variance-authority';
import 'clsx';
import 'tailwind-merge';
import 'better-auth/react';
import 'next-themes';
import 'sonner';
import 'better-auth';
import 'better-auth/adapters/drizzle';
import 'better-auth/react-start';
import 'drizzle-orm/pg-core';
import 'drizzle-orm/postgres-js';
import 'postgres';
import 'node:async_hooks';
import 'valibot';
import 'drizzle-orm';
import 'uuid';
import '@tanstack/react-router/ssr/server';

const SplitComponent = function RouteComponent() {
  const error = Route.useSearch();
  if (error.error) {
    return /* @__PURE__ */ jsx("div", { children: "Token is expired." });
  }
  return /* @__PURE__ */ jsx("div", { children: "Email verify successfully." });
};

export { SplitComponent as component };
//# sourceMappingURL=result-DMkUvz-v.mjs.map
