{"version": 3, "file": "fetchUserId-D1MJg7WG.mjs", "sources": ["../../../../../../src/lib/auth/fetchUserId.ts?tsr-directive-use-server="], "sourcesContent": null, "names": ["fetchUserId_createServerFn_handler", "createServerRpc", "opts", "signal", "fetchUserId.__executeServer", "fetchUserId", "createServerFn", "method", "handler", "request", "getWebRequest", "session", "auth", "api", "getSession", "headers", "userId", "user", "id"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAMC,MAAAA,qCAAAC,eAAAA,CAAA,iEAAA,EAAA,YAAA,EAIS,CAAAC,MAAAC,MAAAA,KAAA;AAAA,EAAA,OAAAC,WAAAA,CAAAA,eAAAA,CAAAF,IAAAA,EAAAC,MAAA,CAAA;AAAA,CAAA;AAFH,MAAME,cAAcC,cAAAA,CAAe;AAAA,EACzCC,MAAAA,EAAQ;AACT,CAAC,CAAA,CAAEC,OAAAA,CAAOR,kCAAAA,EAAC,YAAkC;AAC5C,EAAA,MAAMS,UAAUC,aAAAA,EAAAA;AAChB,EAAA,MAAMC,OAAAA,GAAU,MAAMC,IAAAA,CAAKC,GAAAA,CAAIC,UAAAA,CAAW;AAAA,IAAEC,SAASN,OAAAA,CAAQM;AAAAA,GAAS,CAAA;AACtE,EAAA,MAAMC,MAAAA,GAASL,OAAAA,4BAASM,IAAAA,CAAKC,EAAAA;AAE7B,EAAA,OAAO;AAAA,IACNF;AAAAA,GAAAA;AAEF,CAAC,CAAA;;;;"}