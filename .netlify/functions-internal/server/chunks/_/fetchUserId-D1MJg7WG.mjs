import {
	v as auth,
	b as createServerFn,
	d as createServerRpc,
	t as getWebRequest,
} from "./ssr.mjs";
import "react/jsx-runtime";
import "@tanstack/react-router";
import "@tanstack/react-query";
import "react";
import "@radix-ui/react-slot";
import "class-variance-authority";
import "clsx";
import "tailwind-merge";
import "better-auth/react";
import "next-themes";
import "sonner";
import "better-auth";
import "better-auth/adapters/drizzle";
import "better-auth/react-start";
import "drizzle-orm/pg-core";
import "drizzle-orm/postgres-js";
import "postgres";
import "node:async_hooks";
import "valibot";
import "drizzle-orm";
import "uuid";
import "@tanstack/react-router/ssr/server";

const fetchUserId_createServerFn_handler = createServerRpc(
	"src_lib_auth_fetchUserId_ts--fetchUserId_createServerFn_handler",
	"/_serverFn",
	(opts, signal) => {
		return fetchUserId.__executeServer(opts, signal);
	},
);
const fetchUserId = createServerFn({
	method: "GET",
}).handler(fetchUserId_createServerFn_handler, async () => {
	const request = getWebRequest();
	const session = await auth.api.getSession({
		headers: request.headers,
	});
	const userId = session == null ? void 0 : session.user.id;
	return {
		userId,
	};
});

export { fetchUserId_createServerFn_handler };
//# sourceMappingURL=authMiddleware-D1MJg7WG.mjs.map
