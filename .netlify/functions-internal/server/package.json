{"name": "tanstack-start-example-basic-prod", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@better-auth/utils": "0.2.6", "@better-fetch/fetch": "1.1.18", "@noble/ciphers": "0.6.0", "@noble/hashes": "1.8.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-slot": "1.2.3", "@tanstack/db": "0.1.3", "@tanstack/db-ivm": "0.1.1", "@tanstack/form-core": "1.19.2", "@tanstack/history": "1.131.2", "@tanstack/query-core": "5.83.1", "@tanstack/query-db-collection": "0.2.2", "@tanstack/react-db": "0.1.3", "@tanstack/react-query": "5.85.0", "@tanstack/react-router": "1.131.10", "@tanstack/react-store": "0.7.3", "@tanstack/react-table": "8.21.3", "@tanstack/router-core": "1.131.7", "@tanstack/store": "0.7.2", "@tanstack/table-core": "8.21.3", "better-auth": "1.3.6", "better-call": "1.0.13", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cookie-es": "1.2.2", "defu": "6.1.4", "drizzle-orm": "0.44.4", "fractional-indexing": "3.2.0", "isbot": "5.1.29", "jose": "5.10.0", "kysely": "0.28.5", "lucide-react": "0.525.0", "murmurhash-js": "1.0.0", "nanostores": "0.11.4", "next-themes": "0.4.6", "postgres": "3.4.7", "react": "19.1.1", "react-dom": "19.1.1", "rou3": "0.5.1", "seroval": "1.3.2", "seroval-plugins": "1.3.2", "sonner": "2.0.7", "tailwind-merge": "3.3.1", "tiny-invariant": "1.3.3", "tiny-warning": "1.0.3", "uncrypto": "0.1.3", "use-sync-external-store": "1.5.0", "uuid": "11.1.0", "valibot": "1.1.0", "zod": "3.25.76"}}